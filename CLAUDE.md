# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This repository contains two main machine learning research projects:

1. **Medical Disease Prediction System** (`prediction/`) - A multimodal medical prediction system using Qwen2.5 LLM
2. **OpenVLA Testing Environment** (`test/openvla/`) - Vision-Language-Action models for robotics

## Common Development Commands

### Medical Prediction System (`prediction/`)

```bash
# Install dependencies
cd prediction/
pip install -r requirements.txt

# Train medical prediction model  
python train.py

# Run multi-GPU training
python multi_gpu_train.py

# Test model
python test.py

# Analyze results
python analyze.py

# Compare with traditional ML models
python meach_learning.py

# Process raw data
python data_process.py
```

### OpenVLA System (`test/openvla/`)

```bash
# Install OpenVLA package
cd test/openvla/
pip install -e .

# Install Flash Attention (after main install)
pip install "flash-attn==2.5.5" --no-build-isolation

# Code formatting and linting
make check          # Check code style without changing files
make autoformat     # Auto-format code with black and ruff
make clean          # Remove temporary files

# VLA Training
torchrun --standalone --nnodes 1 --nproc-per-node 8 vla-scripts/train.py \
  --vla.type "prism-dinosiglip-224px+mx-bridge" \
  --data_root_dir <PATH_TO_DATA> \
  --run_root_dir <PATH_TO_LOGS>

# Fine-tuning via LoRA
torchrun --standalone --nnodes 1 --nproc-per-node 1 vla-scripts/finetune.py \
  --vla_path "openvla/openvla-7b" \
  --data_root_dir <PATH_TO_DATA> \
  --dataset_name bridge_orig

# Deploy model as REST API
python vla-scripts/deploy.py

# Convert Prismatic models to HuggingFace format
python vla-scripts/extern/convert_openvla_weights_to_hf.py
```

## Architecture Overview

### Medical Prediction System Architecture

**Core Components:**
- `MultimodalQwenMedical` (`model.py`): VLA-style multimodal architecture combining Qwen2.5 LLM with medical data
- `UnifiedMedicalDataset` (`dataset.py`): Time-series medical dataset supporting arbitrary disease combinations
- Cross-attention layers for disease query mechanism (similar to VLA action queries)

**Data Pipeline:**
- Text features: Symptoms, medical history, demographics
- Numeric features: Lab values, vital signs, physical measurements  
- Time-series prediction: Historical exam data → future disease risk
- Handles class imbalance with Focal Loss, weighted sampling, dynamic thresholds

**Key Files:**
- `model.py` - Multimodal Qwen architecture
- `dataset.py` - Medical dataset processing
- `train.py` - Main training script
- `multi_gpu_train.py` - Distributed training
- `meach_learning.py` - Traditional ML comparison

### OpenVLA Architecture

**Framework Structure:**
- Built on Prismatic VLMs framework
- Supports Vision-Language-Action models for robotics
- FSDP distributed training with Flash Attention

**Core Components:**
- `prismatic/models/vlas/openvla.py` - Main OpenVLA model
- `prismatic/vla/` - VLA-specific components (action tokenizer, datasets)
- `prismatic/models/backbones/` - Vision and language backbone models
- `vla-scripts/` - Training, fine-tuning, deployment scripts

**Training Pipeline:**
- Supports multiple vision backbones (CLIP, DINOv2, SigLIP)
- Language backbones (Llama2, Mistral, Phi)
- RLDS dataset format for robotics data
- Fine-tuning via LoRA or full parameter tuning

## Data Handling

### Medical System Data
- Uses desensitized medical datasets (30M+ exam records over 3 years)
- TSV/CSV format with patient IDs, timestamps, features, disease labels
- Time-series structure: predict future disease risk from current exam data

### OpenVLA Data  
- RLDS format robotics datasets
- Open X-Embodiment (OXE) dataset mixtures
- BridgeData V2 for WidowX robot evaluations
- LIBERO simulation benchmarks

## Model Configurations

### Medical Prediction
- Default Qwen model: `Qwen/Qwen2.5-0.5B` (stored locally)
- Supports binary classification and severity prediction
- Disease queries: Learnable vectors for each target disease
- Imbalance strategies: Focal Loss (α=0.1, γ=3.0), weighted BCE, resampling

### OpenVLA  
- Main model: `openvla-7b` (7.5B parameters)
- Vision backbone: DINOv2 + SigLIP fusion
- Language backbone: Llama2-7B
- Action discretization: 256 bins per action dimension

## Environment Setup

Both projects require:
- Python 3.8+ (medical system), 3.10 recommended (OpenVLA)  
- PyTorch 2.0+ with CUDA support
- Transformers library
- For OpenVLA: Flash Attention 2 for efficient training

## Key Development Patterns

1. **Medical System**: VLA-inspired architecture applying robotics action queries to medical disease prediction
2. **OpenVLA**: Standard vision-language-action model following robotics conventions
3. **Distributed Training**: Both support multi-GPU training via PyTorch FSDP/DDP
4. **Model Conversion**: OpenVLA includes utilities to convert between Prismatic and HuggingFace formats