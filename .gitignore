# 数据文件
*.csv
*.json
*.txt
*.log

# Python缓存文件
*.pyc
*.pyo
*.pyd
*.pyw
*.pyz
__pycache__/
*.py[cod]
*$py.class

# 模型权重文件
*.pth
*.bin
*.safetensors
*.ckpt
*.h5
*.pkl
*.joblib
*.pt

# 模型目录
prediction/Qwen2.5-0.5B/
test/openvla-7b-prismatic/

# 子项目/子模块（嵌入的Git仓库）
test/modified_libero_rlds/
test/openvla/

# 大数据文件
Original_Data_out-of-sensitivity/
*.db
*.zip

# Git LFS对象
.git/lfs/objects/

# 训练输出图像


# IDE文件
.idea/
.vscode/
*.swp
*.swo

# 临时文件
temp_*/
*.tmp
.specstoryprediction/medical_simple.tsv
