#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版医疗时序数据提取器 - 专注稳定性
优先确保能正常工作，然后再优化性能
"""

import pandas as pd
import numpy as np
import os
import re
import time
import argparse
import logging
from pathlib import Path
import gc
from collections import defaultdict, Counter
from tqdm import tqdm


def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("medical_simple.log", encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


def get_memory_usage():
    """获取内存使用(GB)"""
    try:
        import psutil
        return psutil.Process().memory_info().rss / 1024**3
    except:
        return 0


class SimpleDiseaseExtractor:
    """简化版疾病提取器"""

    def __init__(self):
        self.disease_dict = {
            '高血压': ['高血压', '血压高'],
            '糖尿病': ['糖尿病', '1型糖尿病', '2型糖尿病'],
            '冠心病': ['冠心病'],
            '脂肪肝': ['脂肪肝'],
            '高血脂': ['高血脂', '血脂高'],
            '支气管炎': ['支气管炎'],
            '贫血': ['贫血'],
            '肾囊肿': ['肾囊肿'],
            '肥胖': ['肥胖', '超重'],
            '肝炎': ['肝炎'],
            '胃炎': ['胃炎'],
            '关节炎': ['关节炎'],
            '心脏病': ['心脏病'],
            '肺炎': ['肺炎']
        }

        print(f"疾病提取器初始化完成，支持 {len(self.disease_dict)} 种疾病")

    def extract_diseases(self, text_fields):
        """提取疾病"""
        if not text_fields:
            return set()

        # 合并所有文本
        combined_text = " ".join(
            [str(field) for field in text_fields if field and str(field) != 'nan']).lower()

        detected = set()
        for disease, keywords in self.disease_dict.items():
            for keyword in keywords:
                if keyword.lower() in combined_text:
                    detected.add(disease)
                    break

        return detected

    def get_disease_columns(self):
        return sorted(list(self.disease_dict.keys()))


class SimpleMedicalExtractor:
    """简化版医疗数据提取器"""

    def __init__(self, input_dir, output_file, chunk_size=50000):
        self.input_dir = Path(input_dir)
        self.output_file = output_file
        self.chunk_size = chunk_size

        # 初始化组件
        self.logger = setup_logging()
        self.disease_extractor = SimpleDiseaseExtractor()

        # 数据存储 - 简单的字典
        self.patient_records = {}

        # 统计信息
        self.stats = {
            'files_processed': 0,
            'rows_processed': 0,
            'patients_found': 0,
            'diseases_found': 0,
            'errors': 0
        }

        # 疾病相关字段
        self.disease_fields = ['既往史', '健康评价异常1', '健康评价异常2', '健康评价异常3', '症状']

        self.logger.info("简化版医疗时序数据提取器初始化完成")

    def detect_file_info(self, file_path):
        """检测文件编码和分隔符"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    first_line = f.readline()

                # 检测分隔符
                delimiters = [',', '\t', '|', ';']
                delimiter_counts = {d: first_line.count(d) for d in delimiters}
                best_delimiter = max(
                    delimiter_counts.items(), key=lambda x: x[1])[0]

                # 验证能否正常读取
                test_df = pd.read_csv(
                    file_path, encoding=encoding, delimiter=best_delimiter, nrows=10)
                if len(test_df.columns) > 5:  # 至少应该有5列
                    return encoding, best_delimiter

            except Exception as e:
                continue

        return 'utf-8', ','  # 默认值

    def find_id_column(self, columns):
        """查找ID列"""
        id_candidates = [
            '人员ID', 'PERSON_ID', 'uniq_ID', 'ID', 'patient_id',
            '患者ID', '病人ID'
        ]

        for candidate in id_candidates:
            if candidate in columns:
                return candidate

        # 查找包含ID的列
        for col in columns:
            if 'ID' in str(col).upper():
                return col

        # 使用第一列
        return columns[0] if len(columns) > 0 else None

    def extract_year_month(self, filepath):
        """从文件路径提取年月"""
        filename = filepath.name

        # 匹配YYYYMM格式
        match = re.search(r'(\d{4})(\d{2})', filename)
        if match:
            return int(match.group(1)), int(match.group(2))

        # 从目录名提取年份
        for parent in filepath.parents:
            match = re.search(r'(\d{4})', parent.name)
            if match:
                year = int(match.group(1))
                return year, 1

        return 2019, 1

    def process_single_file(self, file_path):
        """处理单个文件"""
        self.logger.info(f"处理文件: {file_path.name}")

        try:
            # 检测文件信息
            encoding, delimiter = self.detect_file_info(file_path)
            year, month = self.extract_year_month(file_path)

            self.logger.info(
                f"  编码: {encoding}, 分隔符: '{delimiter}', 年月: {year}-{month}")

            # 读取文件
            rows_processed = 0
            records_added = 0

            chunk_reader = pd.read_csv(
                file_path,
                encoding=encoding,
                delimiter=delimiter,
                chunksize=self.chunk_size,
                dtype=str,
                on_bad_lines='skip'
            )

            id_column = None

            for chunk_idx, chunk in enumerate(chunk_reader):
                if chunk.empty:
                    continue

                # 第一次确定ID列
                if id_column is None:
                    id_column = self.find_id_column(chunk.columns)
                    if id_column is None:
                        self.logger.error(f"  无法找到ID列，跳过文件")
                        return 0, 0
                    self.logger.info(f"  使用ID列: {id_column}")

                # 处理chunk
                for _, row in chunk.iterrows():
                    try:
                        patient_id = str(row.get(id_column, ''))
                        if not patient_id or patient_id in ['nan', '', 'None']:
                            continue

                        # 清理patient_id
                        patient_id = patient_id.strip().replace(' ', '_')

                        # 提取疾病相关文本
                        disease_texts = []
                        for field in self.disease_fields:
                            if field in row:
                                value = row.get(field)
                                if value and str(value) != 'nan':
                                    disease_texts.append(str(value))

                        # 提取疾病
                        diseases = self.disease_extractor.extract_diseases(
                            disease_texts)

                        # 存储记录
                        record = {
                            'year': year,
                            'month': month,
                            'file': file_path.name,
                            'data': row.tolist(),
                            'diseases': diseases
                        }

                        if patient_id not in self.patient_records:
                            self.patient_records[patient_id] = []

                        self.patient_records[patient_id].append(record)

                        rows_processed += 1
                        records_added += 1

                        if diseases:
                            self.stats['diseases_found'] += len(diseases)

                    except Exception as e:
                        continue

                # 定期报告进度
                if chunk_idx % 10 == 0 and chunk_idx > 0:
                    self.logger.info(
                        f"  处理进度: {rows_processed:,} 行, 内存: {get_memory_usage():.1f}GB")

            self.stats['files_processed'] += 1
            self.stats['rows_processed'] += rows_processed
            self.stats['patients_found'] = len(self.patient_records)

            self.logger.info(
                f"  完成: {rows_processed:,} 行, {records_added:,} 记录")
            return rows_processed, records_added

        except Exception as e:
            self.logger.error(f"  处理失败: {e}")
            self.stats['errors'] += 1
            return 0, 0

    def scan_files(self):
        """扫描所有CSV文件"""
        self.logger.info(f"扫描目录: {self.input_dir}")

        csv_files = []
        for ext in ['*.csv', '*.CSV']:
            files = list(self.input_dir.glob(f"**/{ext}"))
            csv_files.extend(files)

        csv_files = sorted(list(set(csv_files)))

        # 按年份排序
        def sort_key(f):
            year, month = self.extract_year_month(f)
            return (year, month, f.name)

        csv_files.sort(key=sort_key)

        self.logger.info(f"发现 {len(csv_files)} 个CSV文件")
        return csv_files

    def extract_data(self, min_records=2):
        """主提取函数"""
        self.logger.info("=" * 60)
        self.logger.info("开始医疗时序数据提取")
        self.logger.info(f"输入目录: {self.input_dir}")
        self.logger.info(f"输出文件: {self.output_file}")
        self.logger.info(f"块大小: {self.chunk_size:,}")
        self.logger.info(f"最小记录数: {min_records}")
        self.logger.info("=" * 60)

        start_time = time.time()

        # 1. 扫描文件
        csv_files = self.scan_files()
        if not csv_files:
            self.logger.error("未找到CSV文件")
            return False

        # 2. 处理所有文件
        for file_path in tqdm(csv_files, desc="处理文件"):
            self.process_single_file(file_path)

            # 定期内存检查
            if self.stats['files_processed'] % 5 == 0:
                current_memory = get_memory_usage()
                self.logger.info(f"进度检查: 文件 {self.stats['files_processed']}/{len(csv_files)}, "
                                 f"患者 {len(self.patient_records):,}, 内存 {current_memory:.1f}GB")
                gc.collect()

        processing_time = time.time() - start_time

        # 3. 显示处理结果
        self.logger.info(f"\n处理完成!")
        self.logger.info(f"  用时: {processing_time:.1f}s")
        self.logger.info(
            f"  文件: {self.stats['files_processed']}/{len(csv_files)}")
        self.logger.info(f"  行数: {self.stats['rows_processed']:,}")
        self.logger.info(f"  患者: {len(self.patient_records):,}")
        self.logger.info(f"  疾病: {self.stats['diseases_found']:,}")
        self.logger.info(f"  错误: {self.stats['errors']}")

        # 4. 分析时序患者
        return self.analyze_and_output(min_records)

    def analyze_and_output(self, min_records):
        """分析时序患者并输出"""
        self.logger.info("\n分析时序患者...")

        # 统计记录数分布
        record_counts = Counter([len(records)
                                for records in self.patient_records.values()])

        self.logger.info("患者记录分布:")
        for count in sorted(record_counts.keys())[:10]:
            patients = record_counts[count]
            percentage = (patients / len(self.patient_records)) * 100
            self.logger.info(
                f"  {count}条记录: {patients:,} 患者 ({percentage:.1f}%)")

        # 筛选时序患者
        timeseries_patients = {}
        for patient_id, records in self.patient_records.items():
            if len(records) >= min_records:
                # 按年月排序
                sorted_records = sorted(
                    records, key=lambda x: (x['year'], x['month']))
                timeseries_patients[patient_id] = sorted_records

        self.logger.info(
            f"\n找到 {len(timeseries_patients):,} 个时序患者(≥{min_records}条记录)")

        if not timeseries_patients:
            self.logger.error("没有符合条件的时序患者!")
            return False

        # 输出数据
        return self.output_data(timeseries_patients)

    def output_data(self, timeseries_patients):
        """输出时序数据"""
        self.logger.info(f"输出时序数据: {len(timeseries_patients):,} 个患者")

        try:
            disease_columns = self.disease_extractor.get_disease_columns()
            output_rows = []

            # 生成输出数据
            for patient_id, records in tqdm(timeseries_patients.items(), desc="生成数据"):
                for record in records:
                    # 基础数据
                    base_data = record['data']

                    # 疾病向量
                    patient_diseases = record['diseases']
                    disease_vector = [1 if disease in patient_diseases else 0
                                      for disease in disease_columns]

                    # 合并
                    full_row = base_data + disease_vector
                    output_rows.append(full_row)

            # 准备列名 - 从第一个记录获取
            if output_rows:
                first_record = list(timeseries_patients.values())[0][0]
                base_columns = [f'col_{i}' for i in range(
                    len(first_record['data']))]
                all_columns = base_columns + disease_columns

                # 创建DataFrame并保存
                self.logger.info("保存数据到文件...")
                df = pd.DataFrame(output_rows, columns=all_columns)
                df.to_csv(self.output_file, index=False,
                          encoding='utf-8', sep='\t')

                file_size = os.path.getsize(self.output_file) / 1024 / 1024

                self.logger.info("=" * 60)
                self.logger.info("输出完成!")
                self.logger.info(f"  文件: {self.output_file}")
                self.logger.info(f"  大小: {file_size:.1f} MB")
                self.logger.info(f"  时序患者: {len(timeseries_patients):,}")
                self.logger.info(f"  时序记录: {len(output_rows):,}")
                self.logger.info(f"  疾病特征: {len(disease_columns)}")
                self.logger.info("=" * 60)

                return True
            else:
                self.logger.error("没有输出数据!")
                return False

        except Exception as e:
            self.logger.error(f"输出失败: {e}")
            return False


def main():
    parser = argparse.ArgumentParser(description='简化版医疗时序数据提取器')
    parser.add_argument('input_dir', help='输入目录')
    parser.add_argument(
        '-o', '--output', default='medical_simple.tsv', help='输出文件')
    parser.add_argument('-c', '--chunk-size', type=int,
                        default=50000, help='块大小')
    parser.add_argument('-m', '--min-records', type=int,
                        default=2, help='最小记录数')

    args = parser.parse_args()

    if not os.path.exists(args.input_dir):
        print(f"错误: 目录不存在 {args.input_dir}")
        return

    print("简化版医疗时序数据提取器")
    print(f"块大小: {args.chunk_size:,}")
    print(f"最小记录: {args.min_records}")

    # 创建提取器
    extractor = SimpleMedicalExtractor(
        input_dir=args.input_dir,
        output_file=args.output,
        chunk_size=args.chunk_size
    )

    # 开始提取
    success = extractor.extract_data(min_records=args.min_records)

    if success:
        print(f"\n成功! 输出文件: {args.output}")
    else:
        print(f"\n失败!")


if __name__ == "__main__":
    main()
