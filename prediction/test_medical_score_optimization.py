#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗评分优化测试脚本
演示和测试改进后的calculate_medical_score方法
"""

import sys
import numpy as np
from pathlib import Path

# 添加当前目录到路径
sys.path.append('.')

def test_medical_score_improvements():
    """测试医疗评分的改进效果"""
    print("🧪 医疗评分优化测试")
    print("=" * 60)
    
    # 模拟不同场景的医疗指标
    test_scenarios = [
        {
            'name': '高血压模型',
            'disease': '高血压',
            'prevalence': 0.119,
            'precision': 0.480,
            'recall': 0.555,
            'specificity': 0.920,
            'description': '高患病率，平衡表现'
        },
        {
            'name': '糖尿病模型',
            'disease': '糖尿病',
            'prevalence': 0.031,
            'precision': 0.222,
            'recall': 0.329,
            'specificity': 0.950,
            'description': '中患病率，召回率偏低'
        },
        {
            'name': '冠心病模型',
            'disease': '冠心病',
            'prevalence': 0.009,
            'precision': 0.057,
            'recall': 0.133,
            'specificity': 0.980,
            'description': '极低患病率，表现较差'
        },
        {
            'name': '冠心病模型（低召回率）',
            'disease': '冠心病',
            'prevalence': 0.009,
            'precision': 0.150,
            'recall': 0.080,  # 严重低召回率
            'specificity': 0.990,
            'description': '极低患病率，严重低召回率'
        },
        {
            'name': '理想模型',
            'disease': '冠心病',
            'prevalence': 0.009,
            'precision': 0.400,
            'recall': 0.600,
            'specificity': 0.950,
            'description': '极低患病率，理想表现'
        }
    ]
    
    try:
        # 导入训练模块
        from train import SimpleQwenTrainer, MEDICAL_OPTIMIZATION, MedicalScoreConfig
        
        # 创建训练器实例
        trainer = SimpleQwenTrainer()
        
        print("✅ 成功导入训练模块\n")
        
        # 测试不同配置
        test_configurations = [
            ('general', '通用配置'),
            ('cardiovascular', '心血管疾病配置'),
            ('metabolic', '代谢疾病配置'),
            ('chronic', '慢性疾病配置')
        ]
        
        for config_type, config_desc in test_configurations:
            print(f"🔧 测试配置: {config_desc}")
            print("-" * 40)
            
            # 更新配置
            MedicalScoreConfig.update_medical_optimization(config_type)
            
            results = []
            
            for scenario in test_scenarios:
                # 计算传统F1分数
                precision = scenario['precision']
                recall = scenario['recall']
                traditional_f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
                
                # 计算优化后的医疗评分
                medical_score = trainer.calculate_medical_score(
                    precision=scenario['precision'],
                    recall=scenario['recall'],
                    specificity=scenario['specificity'],
                    prevalence=scenario['prevalence'],
                    disease_name=scenario['disease']
                )
                
                results.append({
                    'scenario': scenario['name'],
                    'disease': scenario['disease'],
                    'traditional_f1': traditional_f1,
                    'medical_score': medical_score,
                    'improvement': medical_score / traditional_f1 if traditional_f1 > 0 else 0,
                    'recall': scenario['recall'],
                    'prevalence': scenario['prevalence']
                })
            
            # 显示结果对比
            print(f"\n📊 {config_desc} 结果对比:")
            print(f"{'场景':<20} {'传统F1':<8} {'医疗评分':<8} {'改善倍数':<8} {'召回率':<8}")
            print("-" * 65)
            
            for result in results:
                print(f"{result['scenario']:<20} "
                      f"{result['traditional_f1']:<8.3f} "
                      f"{result['medical_score']:<8.3f} "
                      f"{result['improvement']:<8.2f} "
                      f"{result['recall']:<8.3f}")
            
            print("\n")
        
        # 测试不同惩罚机制
        print("🔥 测试不同召回率惩罚机制")
        print("-" * 40)
        
        test_recalls = [0.05, 0.15, 0.25, 0.35, 0.45, 0.55]
        penalty_types = ['linear', 'exponential', 'stepped']
        
        print(f"{'召回率':<8} {'线性惩罚':<10} {'指数惩罚':<10} {'阶梯惩罚':<10}")
        print("-" * 45)
        
        for recall in test_recalls:
            penalties = {}
            
            for penalty_type in penalty_types:
                # 临时修改配置
                original_type = MEDICAL_OPTIMIZATION['recall_penalty']['type']
                MEDICAL_OPTIMIZATION['recall_penalty']['type'] = penalty_type
                
                # 计算惩罚
                penalty = trainer._calculate_recall_penalty(recall, MEDICAL_OPTIMIZATION['recall_penalty'])
                penalties[penalty_type] = penalty
                
                # 恢复配置
                MEDICAL_OPTIMIZATION['recall_penalty']['type'] = original_type
            
            print(f"{recall:<8.2f} "
                  f"{penalties['linear']:<10.3f} "
                  f"{penalties['exponential']:<10.3f} "
                  f"{penalties['stepped']:<10.3f}")
        
        print("\n🎯 测试患病率调整机制")
        print("-" * 40)
        
        test_prevalences = [0.001, 0.005, 0.01, 0.03, 0.05, 0.10, 0.20]
        test_diseases = ['冠心病', '糖尿病', '高血压']
        
        print(f"{'患病率':<8} {'冠心病':<10} {'糖尿病':<10} {'高血压':<10}")
        print("-" * 45)
        
        for prevalence in test_prevalences:
            adjustments = {}
            
            for disease in test_diseases:
                adjustment = trainer._calculate_prevalence_adjustment(
                    prevalence, disease, MEDICAL_OPTIMIZATION['prevalence_adjustment']
                )
                adjustments[disease] = adjustment
            
            print(f"{prevalence:<8.3f} "
                  f"{adjustments['冠心病']:<10.2f} "
                  f"{adjustments['糖尿病']:<10.2f} "
                  f"{adjustments['高血压']:<10.2f}")
        
        print("\n🎉 医疗评分优化测试完成！")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def demonstrate_medical_score_benefits():
    """演示医疗评分相比传统F1的优势"""
    print("\n🏥 医疗评分优势演示")
    print("=" * 50)
    
    # 模拟两个模型的对比
    models = [
        {
            'name': '模型A（高精确率）',
            'precision': 0.80,
            'recall': 0.25,
            'specificity': 0.95
        },
        {
            'name': '模型B（高召回率）',
            'precision': 0.35,
            'recall': 0.70,
            'specificity': 0.85
        }
    ]
    
    diseases = [
        {'name': '冠心病', 'prevalence': 0.009},
        {'name': '糖尿病', 'prevalence': 0.031},
        {'name': '高血压', 'prevalence': 0.119}
    ]
    
    try:
        from train import SimpleQwenTrainer
        trainer = SimpleQwenTrainer()
        
        for disease in diseases:
            print(f"\n📋 {disease['name']} (患病率: {disease['prevalence']:.1%})")
            print("-" * 30)
            print(f"{'模型':<20} {'传统F1':<8} {'医疗评分':<10} {'推荐':<6}")
            print("-" * 50)
            
            best_f1_model = None
            best_medical_model = None
            best_f1 = 0
            best_medical = 0
            
            for model in models:
                # 传统F1
                f1 = 2 * (model['precision'] * model['recall']) / (model['precision'] + model['recall'])
                
                # 医疗评分
                medical_score = trainer.calculate_medical_score(
                    model['precision'], model['recall'], model['specificity'],
                    disease['prevalence'], disease['name']
                )
                
                # 记录最佳模型
                if f1 > best_f1:
                    best_f1 = f1
                    best_f1_model = model['name']
                
                if medical_score > best_medical:
                    best_medical = medical_score
                    best_medical_model = model['name']
                
                f1_best = "👑F1" if model['name'] == best_f1_model else ""
                medical_best = "👑医疗" if model['name'] == best_medical_model else ""
                recommendation = f1_best + medical_best
                
                print(f"{model['name']:<20} {f1:<8.3f} {medical_score:<10.3f} {recommendation:<6}")
            
            print(f"\n💡 分析:")
            print(f"   传统F1推荐: {best_f1_model}")
            print(f"   医疗评分推荐: {best_medical_model}")
            
            if best_f1_model != best_medical_model:
                print(f"   ⚠️ 推荐不一致！医疗评分更重视召回率，减少漏诊风险")
            else:
                print(f"   ✅ 推荐一致")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 医疗评分优化测试套件")
    print("=" * 60)
    
    # 测试1: 医疗评分改进
    test1_passed = test_medical_score_improvements()
    
    # 测试2: 医疗评分优势演示
    test2_passed = demonstrate_medical_score_benefits()
    
    # 总结
    print("\n📊 测试结果总结")
    print("=" * 30)
    print(f"医疗评分改进测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"医疗评分优势演示: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！医疗评分优化成功")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
