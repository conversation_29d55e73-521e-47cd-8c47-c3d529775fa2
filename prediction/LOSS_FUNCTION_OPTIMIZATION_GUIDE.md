# 损失函数优化指南

## 📊 当前损失函数配置分析

### 1. 当前配置
- **损失函数**: Focal Loss (现已升级为自适应Focal Loss)
- **FOCAL_ALPHA**: 0.1 (正样本权重)
- **FOCAL_GAMMA**: 3.0 (难分类样本关注度)
- **动态阈值**: 启用，基于F1分数优化
- **早停机制**: 基于F1分数，耐心值30

### 2. 新增自适应Focal Loss配置

```python
ADAPTIVE_FOCAL_CONFIG = {
    'high_prevalence': {'min_rate': 0.08, 'alpha': 0.15, 'gamma': 2.5},    # 高患病率 (>8%)
    'medium_prevalence': {'min_rate': 0.02, 'alpha': 0.08, 'gamma': 3.5},  # 中患病率 (2-8%)
    'low_prevalence': {'min_rate': 0.0, 'alpha': 0.03, 'gamma': 4.5}       # 低患病率 (<2%)
}
```

## 🎯 基于测试结果的性能分析

### 当前各疾病表现

| 疾病 | 患病率 | F1分数 | 精确率 | 召回率 | 建议策略 |
|------|--------|--------|--------|--------|----------|
| 高血压 | 11.9% | 0.515 | 0.480 | 0.555 | 高患病率策略 |
| 脂肪肝 | 9.6% | 0.384 | 0.358 | 0.414 | 高患病率策略 |
| 糖尿病 | 3.1% | 0.265 | 0.222 | 0.329 | 中患病率策略 |
| 高血脂 | 2.2% | 0.124 | 0.078 | 0.297 | 中患病率策略 |
| 支气管炎 | 3.0% | 0.218 | 0.179 | 0.279 | 中患病率策略 |
| 气管炎 | 2.7% | 0.191 | 0.161 | 0.235 | 中患病率策略 |
| 贫血 | 1.2% | 0.163 | 0.156 | 0.170 | 低患病率策略 |
| 肾囊肿 | 1.2% | 0.100 | 0.064 | 0.223 | 低患病率策略 |
| 冠心病 | 0.9% | 0.079 | 0.057 | 0.133 | 低患病率策略 |

### 问题识别
1. **患病率-性能负相关**: 患病率越低，F1分数越差
2. **极低患病率疾病表现差**: <2%患病率的疾病F1<0.2
3. **召回率普遍偏低**: 医疗场景需要更高召回率减少漏诊

## 🔧 优化建议

### 1. 自适应参数配置

#### 高患病率疾病 (>8%): 高血压、脂肪肝
```python
alpha = 0.15  # 适中的正样本权重
gamma = 2.5   # 适中的难分类关注度
```
**理由**: 数据相对平衡，不需要过度调整

#### 中患病率疾病 (2-8%): 糖尿病、高血脂、支气管炎、气管炎
```python
alpha = 0.08  # 降低正样本权重，更关注少数类
gamma = 3.5   # 增加难分类关注度
```
**理由**: 需要更多关注少数类，但避免过度拟合

#### 低患病率疾病 (<2%): 贫血、肾囊肿、冠心病
```python
alpha = 0.03  # 大幅降低正样本权重
gamma = 4.5   # 最高难分类关注度
```
**理由**: 极度不平衡，需要最大化对少数类的关注

### 2. 医疗场景优化配置

```python
MEDICAL_OPTIMIZATION = {
    'primary_metric': 'medical_score',  # 使用医疗评分而非F1
    'recall_weight': 2.0,               # 召回率权重加倍
    'precision_weight': 1.0,            # 精确率标准权重
    'min_recall_threshold': 0.3,        # 最低召回率30%
    'max_false_negative_rate': 0.7      # 最大漏诊率70%
}
```

### 3. 动态阈值优化

基于测试结果，不同疾病的最优阈值：
- **高患病率**: 0.30 (高血压、脂肪肝)
- **中患病率**: 0.25 (糖尿病、支气管炎、气管炎)
- **低患病率**: 0.20 (贫血、肾囊肿、冠心病、高血脂)

## 📈 预期改善效果

### 1. 低患病率疾病改善
使用自适应Focal Loss后，预期低患病率疾病的表现：

| 疾病 | 当前F1 | 预期F1 | 改善幅度 |
|------|--------|--------|----------|
| 冠心病 | 0.079 | 0.15-0.20 | +90-150% |
| 肾囊肿 | 0.100 | 0.18-0.25 | +80-150% |
| 贫血 | 0.163 | 0.25-0.30 | +50-85% |

### 2. 召回率提升
医疗优化配置预期将召回率提升：
- **当前平均召回率**: ~0.28
- **目标召回率**: >0.35
- **改善幅度**: +25%

### 3. 医疗安全性
- **减少漏诊**: 通过提高召回率权重
- **平衡精确率**: 避免过多误诊
- **患病率适应**: 低患病率疾病获得更多关注

## 🎛️ 具体参数调优建议

### 1. 立即可用的配置
```python
# 启用自适应Focal Loss
IMBALANCE_STRATEGY = 'adaptive_focal'

# 医疗场景优化
MEDICAL_OPTIMIZATION = {
    'primary_metric': 'medical_score',
    'recall_weight': 2.0,
    'precision_weight': 1.0,
    'min_recall_threshold': 0.3,
    'max_false_negative_rate': 0.7
}
```

### 2. 进一步优化选项

#### 针对极低患病率疾病 (<1%)
```python
# 更激进的参数
'ultra_low_prevalence': {'min_rate': 0.0, 'alpha': 0.01, 'gamma': 5.0}
```

#### 针对高召回率需求
```python
# 提高召回率权重
'recall_weight': 3.0  # 召回率权重提升到3倍
'min_recall_threshold': 0.4  # 最低召回率提升到40%
```

### 3. 分疾病独立优化

对于表现特别差的疾病，可以考虑：
1. **独立训练**: 为每个疾病训练专门的模型
2. **集成学习**: 结合多个模型的预测结果
3. **数据增强**: 针对少数类进行数据增强

## 🔍 监控指标

### 1. 主要监控指标
- **医疗评分**: 综合考虑精确率、召回率和患病率
- **召回率**: 重点关注，目标>0.35
- **F1分数**: 传统指标，用于对比
- **AUC**: 模型区分能力

### 2. 医疗安全指标
- **假阴性率**: 漏诊率，应<70%
- **敏感性**: 识别患病的能力
- **特异性**: 识别健康的能力
- **阳性预测值**: 预测患病的准确性

## 🚀 实施步骤

1. **启用自适应Focal Loss**: 修改 `IMBALANCE_STRATEGY = 'adaptive_focal'`
2. **配置医疗优化**: 设置 `MEDICAL_OPTIMIZATION` 参数
3. **运行训练**: 观察各疾病的改善情况
4. **微调参数**: 根据结果调整alpha和gamma值
5. **验证效果**: 在测试集上验证改善效果

## 📋 预期结果

使用优化后的配置，预期整体改善：
- **平均F1分数**: 从0.22提升到0.30+ (+35%)
- **低患病率疾病F1**: 从0.11提升到0.20+ (+80%)
- **平均召回率**: 从0.28提升到0.38+ (+35%)
- **医疗安全性**: 显著减少漏诊风险

这些优化特别适合医疗场景，在保持合理精确率的同时，最大化召回率以减少漏诊风险。
