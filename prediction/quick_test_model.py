#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速模型测试代码 - 使用小样本测试qwen_medical_model_08_16.pth模型效果
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, roc_auc_score,
    confusion_matrix, classification_report
)
from torch.utils.data import DataLoader
import warnings
import os
from tqdm import tqdm
import json
from datetime import datetime

warnings.filterwarnings('ignore')

# 导入自定义模块
from dataset import load_unified_timeseries_data
from model import create_multimodal_qwen_model

# ==================== 配置区域 ====================

# 📂 文件路径配置
DATA_PATH = "/root/work_speace/prediction/coronary_timeseries_data_fixed.csv"
MODEL_PATH = "/root/work_speace/prediction/qwen_medical_model_08_16.pth"
QWEN_MODEL_NAME = '/root/work_speace/prediction/Qwen2.5-0.5B'

# 🎯 目标疾病（需要与训练时保持一致）
TARGET_DISEASES = ['高血压','脂肪肝','糖尿病','高血脂',"支气管炎","气管炎","贫血","肾囊肿",'冠心病']

# 📝 特征配置（需要与训练时保持一致）
TEXT_FEATURES = [
    '症状', '既往史', '家族史', '性别', '年龄num',
    '职业', '吸烟状况', '饮酒频率', '饮食习惯'
]

NUMERIC_FEATURES = [
    '体温', '脉率', '呼吸频率', '左侧收缩压', '左侧舒张压',
    '右侧收缩压', '右侧舒张压', '身高', '体重', '腰围', '体质指数',
    '血红蛋白', '白细胞', '血小板', '空腹血糖MMOL',
    '总胆固醇', '甘油三酯', '血清肌酐', '血尿素'
]

# 🎯 预测配置
PREDICTION_MODE = 'binary'
BATCH_SIZE = 32
PREDICTION_THRESHOLD = 0.5
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 🔥 限制样本数用于快速测试
MAX_SAMPLES = 5000  # 限制为5000样本进行快速测试

def load_model():
    """加载训练好的模型"""
    print(f"📥 加载模型: {MODEL_PATH}")
    
    if not os.path.exists(MODEL_PATH):
        print(f"❌ 模型文件不存在: {MODEL_PATH}")
        return None
        
    try:
        # 创建模型结构
        model = create_multimodal_qwen_model(
            target_diseases=TARGET_DISEASES,
            text_features=TEXT_FEATURES,
            numeric_features=NUMERIC_FEATURES,
            qwen_model_name=QWEN_MODEL_NAME,
            prediction_mode=PREDICTION_MODE
        )
        
        # 加载模型权重
        print(f"⏳ 正在加载模型权重...")
        checkpoint = torch.load(MODEL_PATH, map_location=DEVICE)
        
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ 模型权重加载成功")
            
            # 打印模型训练信息
            if 'epoch' in checkpoint:
                print(f"   训练轮数: {checkpoint['epoch']}")
            if 'f1' in checkpoint:
                print(f"   训练时最佳F1: {checkpoint['f1']:.4f}")
            if 'auc' in checkpoint:
                print(f"   训练时最佳AUC: {checkpoint['auc']:.4f}")
            if 'strategy' in checkpoint:
                print(f"   不均衡策略: {checkpoint['strategy']}")
        else:
            print(f"❌ 模型文件格式错误")
            return None
            
        # 移动到设备并设置为评估模式
        model.to(DEVICE)
        model.eval()
        
        return model
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def load_test_data():
    """加载测试数据"""
    print(f"📂 加载测试数据（限制{MAX_SAMPLES}样本）...")
    
    # 加载数据（使用小样本进行快速测试）
    train_dataset, val_dataset, test_dataset, scaler, feature_info = load_unified_timeseries_data(
        csv_path=DATA_PATH,
        target_diseases=TARGET_DISEASES,
        prediction_mode=PREDICTION_MODE,
        test_size=0.2,  # 与训练时保持一致
        max_samples=MAX_SAMPLES,  # 限制样本数
        time_gap_years=1
    )
    
    if test_dataset is None:
        print(f"❌ 测试数据加载失败")
        return None, None
        
    print(f"✅ 测试数据加载成功:")
    print(f"   训练集: {len(train_dataset)} 样本")
    print(f"   验证集: {len(val_dataset)} 样本") 
    print(f"   测试集: {len(test_dataset)} 样本")
    
    # 创建数据加载器
    test_loader = DataLoader(
        test_dataset,
        batch_size=BATCH_SIZE,
        shuffle=False,
        num_workers=1,
        pin_memory=False
    )
    
    return test_dataset, test_loader

def convert_batch_to_patient_data(batch):
    """将batch转换为patient_data格式（与训练代码保持一致）"""
    texts = batch['text']
    numeric = batch['numeric'].to(DEVICE)
    labels = batch['label'].to(DEVICE)
    
    patient_data = []
    for i in range(len(texts)):
        patient_dict = {}
        text = texts[i]
        
        # 解析年龄
        if "年龄" in text:
            try:
                age_start = text.find("年龄") + 2
                age_end = text.find("岁", age_start)
                if age_end > age_start:
                    age_str = text[age_start:age_end].strip(": ")
                    patient_dict['年龄num'] = float(age_str)
                else:
                    patient_dict['年龄num'] = 0.0
            except:
                patient_dict['年龄num'] = 0.0
        
        # 解析症状
        if "症状:" in text:
            try:
                symptom_start = text.find("症状:") + 3
                symptom_end = text.find(" ", symptom_start)
                if symptom_end > symptom_start:
                    patient_dict['症状'] = text[symptom_start:symptom_end]
                else:
                    patient_dict['症状'] = text[symptom_start:symptom_start+10]
            except:
                patient_dict['症状'] = '无症状'
        
        # 添加数值特征
        numeric_values = numeric[i].cpu().numpy()
        for j, feature_name in enumerate(NUMERIC_FEATURES):
            if j < len(numeric_values):
                patient_dict[feature_name] = float(numeric_values[j])
        
        patient_data.append(patient_dict)
    
    return patient_data, labels

def evaluate_model(model, test_loader):
    """评估模型性能"""
    print(f"🔍 开始模型评估...")
    
    model.eval()
    all_probs = []
    all_labels = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="评估中"):
            # 转换数据格式
            patient_data, labels = convert_batch_to_patient_data(batch)
            
            # 前向传播
            probs, logits = model(patient_data)
            
            all_probs.append(probs.cpu())
            all_labels.append(labels.cpu())
    
    # 合并结果
    all_probs = torch.cat(all_probs, dim=0).numpy()
    all_labels = torch.cat(all_labels, dim=0).numpy()
    
    print(f"✅ 模型评估完成")
    print(f"   预测概率形状: {all_probs.shape}")
    print(f"   真实标签形状: {all_labels.shape}")
    
    return all_probs, all_labels

def calculate_disease_metrics(all_probs, all_labels):
    """计算每个疾病的详细指标"""
    print(f"📊 计算各疾病详细指标...")
    print(f"="*60)
    
    disease_metrics = {}
    
    for i, disease in enumerate(TARGET_DISEASES):
        print(f"\n=== {disease} ===")
        
        if len(TARGET_DISEASES) == 1:
            probs = all_probs.flatten()
            labels = all_labels.flatten()
        else:
            probs = all_probs[:, i]
            labels = all_labels[:, i]
        
        # 使用默认阈值
        threshold = PREDICTION_THRESHOLD
        preds = (probs > threshold).astype(int)
        
        # 基本指标
        accuracy = accuracy_score(labels, preds)
        precision = precision_score(labels, preds, zero_division=0)
        recall = recall_score(labels, preds, zero_division=0)
        f1 = f1_score(labels, preds, zero_division=0)
        
        # AUC
        try:
            auc = roc_auc_score(labels, probs)
        except:
            auc = 0.0
        
        # 混淆矩阵
        tn, fp, fn, tp = confusion_matrix(labels, preds).ravel()
        
        # 医疗评价指标
        sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0.0  # 敏感性
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0.0  # 特异性
        ppv = tp / (tp + fp) if (tp + fp) > 0 else 0.0  # 阳性预测值
        npv = tn / (tn + fn) if (tn + fn) > 0 else 0.0  # 阴性预测值
        
        # 样本分布
        pred_positive = int(preds.sum())
        true_positive = int(labels.sum())
        total_samples = len(labels)
        
        disease_metrics[disease] = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'auc': auc,
            'sensitivity': sensitivity,
            'specificity': specificity,
            'ppv': ppv,
            'npv': npv,
            'tp': int(tp),
            'tn': int(tn),
            'fp': int(fp),
            'fn': int(fn),
            'pred_positive': pred_positive,
            'true_positive': true_positive,
            'total_samples': total_samples
        }
        
        # 打印结果
        print(f"📊 基本指标:")
        print(f"   准确率: {accuracy:.4f}")
        print(f"   精确率: {precision:.4f}")
        print(f"   召回率: {recall:.4f}")
        print(f"   F1分数: {f1:.4f}")
        print(f"   AUC: {auc:.4f}")
        
        print(f"🏥 医疗指标:")
        print(f"   敏感性: {sensitivity:.4f} (真阳性率)")
        print(f"   特异性: {specificity:.4f} (真阴性率)")
        print(f"   阳性预测值: {ppv:.4f}")
        print(f"   阴性预测值: {npv:.4f}")
        
        print(f"🎯 混淆矩阵:")
        print(f"   真阳性(TP): {tp:4d}   假阳性(FP): {fp:4d}")
        print(f"   假阴性(FN): {fn:4d}   真阴性(TN): {tn:4d}")
        
        print(f"📈 样本分布:")
        print(f"   预测阳性: {pred_positive:4d}/{total_samples} ({pred_positive/total_samples*100:.2f}%)")
        print(f"   真实阳性: {true_positive:4d}/{total_samples} ({true_positive/total_samples*100:.2f}%)")
    
    return disease_metrics

def print_summary(disease_metrics):
    """打印汇总统计"""
    print(f"\n" + "="*60)
    print(f"📊 测试结果汇总 (基于{MAX_SAMPLES}样本)")
    print(f"="*60)
    
    # 汇总统计
    metrics_names = ['f1', 'precision', 'recall', 'auc', 'sensitivity', 'specificity']
    avg_metrics = {}
    
    for metric in metrics_names:
        avg_metrics[metric] = sum(disease_metrics[disease][metric] for disease in disease_metrics) / len(disease_metrics)
    
    print(f"🎯 整体平均指标:")
    print(f"   平均F1分数: {avg_metrics['f1']:.4f}")
    print(f"   平均精确率: {avg_metrics['precision']:.4f}")
    print(f"   平均召回率: {avg_metrics['recall']:.4f}")
    print(f"   平均AUC: {avg_metrics['auc']:.4f}")
    print(f"   平均敏感性: {avg_metrics['sensitivity']:.4f}")
    print(f"   平均特异性: {avg_metrics['specificity']:.4f}")
    
    # 最佳和最差疾病
    best_disease = max(disease_metrics.items(), key=lambda x: x[1]['f1'])
    worst_disease = min(disease_metrics.items(), key=lambda x: x[1]['f1'])
    
    print(f"\n🏆 F1分数最佳疾病: {best_disease[0]} (F1: {best_disease[1]['f1']:.4f})")
    print(f"⚠️ F1分数最差疾病: {worst_disease[0]} (F1: {worst_disease[1]['f1']:.4f})")
    
    # 按F1分数排序
    print(f"\n📈 各疾病F1分数排名:")
    sorted_diseases = sorted(disease_metrics.items(), key=lambda x: x[1]['f1'], reverse=True)
    for i, (disease, metrics) in enumerate(sorted_diseases, 1):
        print(f"   {i:2d}. {disease:8s}: F1={metrics['f1']:.4f}, AUC={metrics['auc']:.4f}, 真阳性={metrics['true_positive']:4d}")

def main():
    """主函数"""
    print(f"🎯 医疗预测模型快速测试")
    print(f"📊 限制样本数: {MAX_SAMPLES}")
    print(f"🎮 使用设备: {DEVICE}")
    print(f"="*50)
    
    # 检查文件是否存在
    if not os.path.exists(MODEL_PATH):
        print(f"❌ 模型文件不存在: {MODEL_PATH}")
        return
    
    if not os.path.exists(DATA_PATH):
        print(f"❌ 数据文件不存在: {DATA_PATH}")
        return
    
    # 1. 加载模型
    model = load_model()
    if model is None:
        return
    
    # 2. 加载测试数据
    test_dataset, test_loader = load_test_data()
    if test_dataset is None:
        return
    
    # 3. 评估模型
    all_probs, all_labels = evaluate_model(model, test_loader)
    
    # 4. 计算详细指标
    disease_metrics = calculate_disease_metrics(all_probs, all_labels)
    
    # 5. 打印汇总
    print_summary(disease_metrics)
    
    # 6. 保存简单结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"quick_test_results_{timestamp}.json"
    
    simple_results = {
        'test_info': {
            'timestamp': timestamp,
            'model_path': MODEL_PATH,
            'max_samples': MAX_SAMPLES,
            'target_diseases': TARGET_DISEASES
        },
        'disease_metrics': disease_metrics,
        'summary': {
            'avg_f1': sum(metrics['f1'] for metrics in disease_metrics.values()) / len(disease_metrics),
            'avg_precision': sum(metrics['precision'] for metrics in disease_metrics.values()) / len(disease_metrics),
            'avg_recall': sum(metrics['recall'] for metrics in disease_metrics.values()) / len(disease_metrics),
            'avg_auc': sum(metrics['auc'] for metrics in disease_metrics.values()) / len(disease_metrics)
        }
    }
    
    try:
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(simple_results, f, indent=2, ensure_ascii=False)
        print(f"\n💾 测试结果已保存: {results_file}")
    except Exception as e:
        print(f"⚠️ 保存测试结果失败: {e}")
    
    print(f"\n✅ 快速测试完成!")

if __name__ == "__main__":
    main()