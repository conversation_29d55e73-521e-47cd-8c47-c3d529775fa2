#!/usr/bin/env python3
"""
演示train.py新增功能的使用方法
"""
import json
import datetime
from pathlib import Path

def demo_enhanced_metrics():
    """演示增强的医疗评价指标"""
    print("🎯 演示：增强的医疗评价指标")
    print("="*60)
    
    # 模拟验证函数返回的扩展指标
    enhanced_metrics = {
        'val_loss': 0.1234,
        'accuracy': 0.9718,
        'precision': 0.6892,  # PPV (阳性预测值)
        'recall': 0.5413,     # Sensitivity (敏感性)
        'f1': 0.5413,
        'auc': 0.8906,
        'pred_positive': 1205,
        'pred_negative': 61439,
        'true_positive': 1873,
        'true_negative': 60771,
        # 新增的医疗评价指标
        'sensitivity': 0.5413,  # 敏感性 = recall
        'specificity': 0.9891,  # 特异性 = TN/(TN+FP)
        'ppv': 0.6892,         # 阳性预测值 = precision  
        'npv': 0.9752,         # 阴性预测值 = TN/(TN+FN)
        'tp': 1014,            # 真阳性
        'tn': 60771,           # 真阴性
        'fp': 191,             # 假阳性
        'fn': 859              # 假阴性
    }
    
    print("📊 完整的医疗AI评价指标报告:")
    print(f"   🎯 总体性能:")
    print(f"      准确率 (Accuracy): {enhanced_metrics['accuracy']:.4f}")
    print(f"      F1分数: {enhanced_metrics['f1']:.4f}")
    print(f"      AUC: {enhanced_metrics['auc']:.4f}")
    
    print(f"   🏥 医疗专业指标:")
    print(f"      敏感性 (Sensitivity): {enhanced_metrics['sensitivity']:.4f}")
    print(f"         → 识别患病者的能力: {enhanced_metrics['sensitivity']*100:.1f}%")
    print(f"      特异性 (Specificity): {enhanced_metrics['specificity']:.4f}")
    print(f"         → 识别健康者的能力: {enhanced_metrics['specificity']*100:.1f}%")
    print(f"      阳性预测值 (PPV): {enhanced_metrics['ppv']:.4f}")
    print(f"         → 阳性诊断的准确性: {enhanced_metrics['ppv']*100:.1f}%")
    print(f"      阴性预测值 (NPV): {enhanced_metrics['npv']:.4f}")
    print(f"         → 阴性诊断的准确性: {enhanced_metrics['npv']*100:.1f}%")
    
    print(f"   📋 混淆矩阵:")
    print(f"      真阳性 (TP): {enhanced_metrics['tp']}")
    print(f"      真阴性 (TN): {enhanced_metrics['tn']}")
    print(f"      假阳性 (FP): {enhanced_metrics['fp']}")
    print(f"      假阴性 (FN): {enhanced_metrics['fn']}")
    
    return enhanced_metrics

def demo_training_results_export():
    """演示训练结果导出功能"""
    print("\n💾 演示：训练结果导出功能")
    print("="*60)
    
    # 模拟完整的训练结果数据
    training_results = {
        'training_info': {
            'timestamp': '20240816_143022',
            'total_epochs': 4,
            'early_stopped': True,
            'best_epoch': 4
        },
        'config': {
            'target_diseases': ['高血压', '脂肪肝', '糖尿病', '高血脂', '支气管炎', 
                              '气管炎', '贫血', '肾囊肿', '冠心病'],
            'qwen_model': '/root/work_speace/prediction/Qwen2.5-0.5B',
            'learning_rate': 1e-5,
            'batch_size': 64,
            'epochs': 10,
            'strategy': 'focal_loss',
            'threshold': 0.5
        },
        'training_curves': {
            'train_losses': [33.60, 5.94, 5.65, 5.61],
            'val_losses': [2.85, 1.92, 1.67, 1.45]
        },
        'epoch_metrics': [
            {'epoch': 1, 'train_loss': 33.60, 'f1': 0.018, 'auc': 0.621},
            {'epoch': 2, 'train_loss': 5.94, 'f1': 0.167, 'auc': 0.834},
            {'epoch': 3, 'train_loss': 5.65, 'f1': 0.421, 'auc': 0.872},
            {'epoch': 4, 'train_loss': 5.61, 'f1': 0.541, 'auc': 0.891}
        ],
        'best_model_performance': {
            'f1': 0.5413,
            'auc': 0.8906,
            'detailed_metrics': {
                'epoch': 4,
                'accuracy': 0.9718,
                'sensitivity': 0.5413,
                'specificity': 0.9891,
                'ppv': 0.6892,
                'npv': 0.9752
            }
        },
        'medical_evaluation_summary': {
            'description': 'Medical AI model evaluation metrics',
            'metrics_explanation': {
                'sensitivity': 'True Positive Rate - ability to identify disease cases',
                'specificity': 'True Negative Rate - ability to identify healthy cases', 
                'ppv': 'Positive Predictive Value - precision of positive predictions',
                'npv': 'Negative Predictive Value - precision of negative predictions',
                'f1': 'Harmonic mean of precision and recall',
                'auc': 'Area Under ROC Curve - overall discrimination ability'
            }
        }
    }
    
    # 演示保存到JSON文件
    results_dir = Path("demo_results")
    results_dir.mkdir(exist_ok=True)
    demo_file = results_dir / "demo_training_results_20240816_143022.json"
    
    with open(demo_file, 'w', encoding='utf-8') as f:
        json.dump(training_results, f, indent=2, ensure_ascii=False)
    
    print(f"📄 训练结果已保存到: {demo_file}")
    print(f"📊 文件大小: {demo_file.stat().st_size / 1024:.1f} KB")
    
    # 展示部分内容
    print(f"\n📋 训练结果摘要:")
    print(f"   训练时间: {training_results['training_info']['timestamp']}")
    print(f"   总轮数: {training_results['training_info']['total_epochs']}")
    print(f"   最佳F1: {training_results['best_model_performance']['f1']:.4f}")
    print(f"   最佳AUC: {training_results['best_model_performance']['auc']:.4f}")
    print(f"   目标疾病数: {len(training_results['config']['target_diseases'])}")
    
    print(f"\n🔍 详细医疗指标:")
    best_metrics = training_results['best_model_performance']['detailed_metrics']
    print(f"   敏感性: {best_metrics['sensitivity']:.4f}")
    print(f"   特异性: {best_metrics['specificity']:.4f}")
    print(f"   阳性预测值: {best_metrics['ppv']:.4f}")
    print(f"   阴性预测值: {best_metrics['npv']:.4f}")
    
    return demo_file

def demo_usage_examples():
    """演示使用示例"""
    print("\n💡 演示：如何使用新功能")
    print("="*60)
    
    print("1. 🚀 启动训练 (train.py已自动包含新功能):")
    print("   torchrun --standalone --nnodes=1 --nproc-per-node=8 train.py")
    
    print("\n2. 📊 训练过程中会显示扩展指标:")
    print("   📊 训练损失: 2.8214")
    print("   📊 验证损失: 1.4523")
    print("   📊 准确率: 0.9718")
    print("   📊 精确率: 0.6892")
    print("   📊 召回率: 0.5413")
    print("   📊 F1分数: 0.5413")
    print("   📊 AUC: 0.8906")
    print("   📊 敏感性: 0.5413")
    print("   📊 特异性: 0.9891")
    print("   📊 阳性预测值: 0.6892")
    print("   📊 阴性预测值: 0.9752")
    
    print("\n3. 💾 训练完成后自动保存详细结果:")
    print("   📄 results/training_results_YYYYMMDD_HHMMSS.json")
    print("   📄 包含完整的训练历史和最终评估结果")
    
    print("\n4. 📈 分析结果的Python代码示例:")
    print("""   import json
   with open('results/training_results_YYYYMMDD_HHMMSS.json', 'r') as f:
       results = json.load(f)
   
   # 获取最佳模型的医疗指标
   metrics = results['best_model_performance']['detailed_metrics']
   print(f"模型敏感性: {metrics['sensitivity']:.4f}")
   print(f"模型特异性: {metrics['specificity']:.4f}")
   
   # 绘制训练曲线
   import matplotlib.pyplot as plt
   epochs = range(1, len(results['training_curves']['train_losses']) + 1)
   plt.plot(epochs, results['training_curves']['train_losses'], label='Train Loss')
   plt.plot(epochs, results['training_curves']['val_losses'], label='Val Loss')
   plt.legend()
   plt.show()""")

if __name__ == "__main__":
    print("🎉 演示 train.py 新增功能")
    print("🏥 医疗AI模型训练增强版")
    print("="*60)
    
    # 演示新的评价指标
    enhanced_metrics = demo_enhanced_metrics()
    
    # 演示结果导出功能
    demo_file = demo_training_results_export()
    
    # 演示使用方法
    demo_usage_examples()
    
    print("\n" + "="*60)
    print("✅ 所有新功能演示完成！")
    print("🚀 现在可以使用增强版的train.py进行训练")
    print(f"📄 演示文件保存在: {demo_file}")