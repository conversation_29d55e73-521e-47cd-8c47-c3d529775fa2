#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的模型测试代码 - 测试qwen_medical_model_08_16.pth模型效果
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, roc_auc_score,
    confusion_matrix, classification_report
)
from torch.utils.data import DataLoader
import warnings
import os
from tqdm import tqdm
import json
from datetime import datetime
import pickle
import hashlib
from pathlib import Path

warnings.filterwarnings('ignore')

# 导入自定义模块
from dataset import load_unified_timeseries_data
from model import create_multimodal_qwen_model

# ==================== 配置区域 ====================

# 📂 文件路径配置
DATA_PATH = "/root/work_speace/prediction/coronary_timeseries_data_fixed.csv"
MODEL_PATH = "/root/work_speace/prediction/qwen_medical_model_08_16.pth"
QWEN_MODEL_NAME = '/root/work_speace/prediction/Qwen2.5-0.5B'

# 🎯 目标疾病（需要与训练时保持一致）
TARGET_DISEASES = ['高血压','脂肪肝','糖尿病','高血脂',"支气管炎","气管炎","贫血","肾囊肿",'冠心病']

# 💾 数据缓存配置（与训练时保持一致）
CACHE_DIR = "/root/work_speace/prediction/data_cache"
USE_CACHE = True  # 使用缓存以避免重复构建序列对

# 📝 特征配置（需要与训练时保持一致）
TEXT_FEATURES = [
    '症状', '既往史', '家族史', '性别', '年龄num',
    '职业', '吸烟状况', '饮酒频率', '饮食习惯'
]

NUMERIC_FEATURES = [
    '体温', '脉率', '呼吸频率', '左侧收缩压', '左侧舒张压',
    '右侧收缩压', '右侧舒张压', '身高', '体重', '腰围', '体质指数',
    '血红蛋白', '白细胞', '血小板', '空腹血糖MMOL',
    '总胆固醇', '甘油三酯', '血清肌酐', '血尿素'
]

# 🎯 预测配置
PREDICTION_MODE = 'binary'
BATCH_SIZE = 64
PREDICTION_THRESHOLD = 0.5
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# ==================== 缓存处理函数 ====================

def get_data_cache_key(csv_path, target_diseases, prediction_mode, max_samples, time_gap_years, test_size):
    """生成数据缓存的唯一键（与训练时保持一致）"""
    # 获取文件修改时间
    file_mtime = os.path.getmtime(csv_path)
    
    # 创建参数字符串
    params_str = f"{csv_path}_{target_diseases}_{prediction_mode}_{max_samples}_{time_gap_years}_{test_size}_{file_mtime}"
    
    # 生成MD5哈希
    cache_key = hashlib.md5(params_str.encode()).hexdigest()
    return cache_key

def load_processed_data(cache_path):
    """从缓存加载处理好的数据"""
    if not cache_path.exists():
        return None
        
    print(f"📂 从缓存加载数据: {cache_path}")
    
    try:
        with open(cache_path, 'rb') as f:
            data = pickle.load(f)
        print(f"✅ 数据缓存加载成功")
        return data
    except Exception as e:
        print(f"⚠️ 缓存加载失败: {e}")
        return None

def load_data_with_cache(csv_path, target_diseases, prediction_mode='binary',
                        test_size=0.2, max_samples=None, time_gap_years=1):
    """带缓存的数据加载函数（与训练时保持一致）"""
    
    if not USE_CACHE:
        print(f"🔄 禁用缓存，直接加载数据...")
        return load_unified_timeseries_data(
            csv_path=csv_path,
            target_diseases=target_diseases,
            prediction_mode=prediction_mode,
            test_size=test_size,
            max_samples=max_samples,
            time_gap_years=time_gap_years
        )
    
    # 生成缓存键和路径
    cache_key = get_data_cache_key(csv_path, target_diseases, prediction_mode, max_samples, time_gap_years, test_size)
    cache_path = Path(CACHE_DIR) / f"{cache_key}.pkl"
    
    print(f"🔍 检查数据缓存: {cache_key[:8]}...")
    
    # 尝试从缓存加载
    cached_data = load_processed_data(cache_path)
    if cached_data is not None:
        print(f"🚀 使用缓存数据，跳过数据处理")
        return cached_data
    
    # 缓存不存在，重新处理数据
    print(f"🔄 缓存不存在，开始处理数据...")
    
    data = load_unified_timeseries_data(
        csv_path=csv_path,
        target_diseases=target_diseases,
        prediction_mode=prediction_mode,
        test_size=test_size,
        max_samples=max_samples,
        time_gap_years=time_gap_years
    )
    
    return data

# ==================== 测试代码 ====================

class ModelTester:
    """模型测试器"""
    
    def __init__(self):
        self.device = DEVICE
        self.model = None
        self.test_results = {}
        
        print(f"🎯 模型测试器初始化")
        print(f"   设备: {self.device}")
        print(f"   目标疾病: {TARGET_DISEASES}")
        print(f"   模型路径: {MODEL_PATH}")
        
    def load_model(self):
        """加载训练好的模型"""
        print(f"📥 加载模型: {MODEL_PATH}")
        
        if not os.path.exists(MODEL_PATH):
            print(f"❌ 模型文件不存在: {MODEL_PATH}")
            return False
            
        try:
            # 创建模型结构
            self.model = create_multimodal_qwen_model(
                target_diseases=TARGET_DISEASES,
                text_features=TEXT_FEATURES,
                numeric_features=NUMERIC_FEATURES,
                qwen_model_name=QWEN_MODEL_NAME,
                prediction_mode=PREDICTION_MODE
            )
            
            # 加载模型权重
            checkpoint = torch.load(MODEL_PATH, map_location=self.device)
            
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
                print(f"✅ 模型权重加载成功")
                
                # 打印模型训练信息
                if 'epoch' in checkpoint:
                    print(f"   训练轮数: {checkpoint['epoch']}")
                if 'f1' in checkpoint:
                    print(f"   最佳F1: {checkpoint['f1']:.4f}")
                if 'auc' in checkpoint:
                    print(f"   最佳AUC: {checkpoint['auc']:.4f}")
                if 'strategy' in checkpoint:
                    print(f"   不均衡策略: {checkpoint['strategy']}")
            else:
                print(f"❌ 模型文件格式错误")
                return False
                
            # 移动到设备并设置为评估模式
            self.model.to(self.device)
            self.model.eval()
            
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def load_test_data(self):
        """加载测试数据"""
        print(f"📂 加载测试数据...")
        
        # 使用缓存加载数据（与训练时相同的参数）
        train_dataset, val_dataset, test_dataset, scaler, feature_info = load_data_with_cache(
            csv_path=DATA_PATH,
            target_diseases=TARGET_DISEASES,
            prediction_mode=PREDICTION_MODE,
            test_size=0.2,  # 与训练时保持一致
            max_samples=None,  # 使用全部数据，与train.py中MAX_SAMPLES = None保持一致
            time_gap_years=1  # 与训练时保持一致
        )
        
        if test_dataset is None:
            print(f"❌ 测试数据加载失败")
            return None, None
            
        print(f"✅ 测试数据加载成功: {len(test_dataset)} 样本")
        
        # 创建数据加载器
        test_loader = DataLoader(
            test_dataset,
            batch_size=BATCH_SIZE,
            shuffle=False,
            num_workers=2,
            pin_memory=False
        )
        
        return test_dataset, test_loader
    
    def convert_batch_to_patient_data(self, batch):
        """将batch转换为patient_data格式（与训练代码保持一致）"""
        texts = batch['text']
        numeric = batch['numeric'].to(self.device)
        labels = batch['label'].to(self.device)
        
        patient_data = []
        for i in range(len(texts)):
            patient_dict = {}
            text = texts[i]
            
            # 解析年龄
            if "年龄" in text:
                try:
                    age_start = text.find("年龄") + 2
                    age_end = text.find("岁", age_start)
                    if age_end > age_start:
                        age_str = text[age_start:age_end].strip(": ")
                        patient_dict['年龄num'] = float(age_str)
                    else:
                        patient_dict['年龄num'] = 0.0
                except:
                    patient_dict['年龄num'] = 0.0
            
            # 解析症状
            if "症状:" in text:
                try:
                    symptom_start = text.find("症状:") + 3
                    symptom_end = text.find(" ", symptom_start)
                    if symptom_end > symptom_start:
                        patient_dict['症状'] = text[symptom_start:symptom_end]
                    else:
                        patient_dict['症状'] = text[symptom_start:symptom_start+10]
                except:
                    patient_dict['症状'] = '无症状'
            
            # 添加数值特征
            numeric_values = numeric[i].cpu().numpy()
            for j, feature_name in enumerate(NUMERIC_FEATURES):
                if j < len(numeric_values):
                    patient_dict[feature_name] = float(numeric_values[j])
            
            patient_data.append(patient_dict)
        
        return patient_data, labels
    
    def find_optimal_threshold_per_disease(self, all_probs, all_labels):
        """为每个疾病找到最优阈值"""
        optimal_thresholds = {}
        
        print(f"🎯 为每个疾病寻找最优阈值...")
        
        for i, disease in enumerate(TARGET_DISEASES):
            if len(TARGET_DISEASES) == 1:
                probs = all_probs.flatten()
                labels = all_labels.flatten()
            else:
                probs = all_probs[:, i]
                labels = all_labels[:, i]
            
            best_f1 = 0
            best_threshold = 0.5
            
            # 测试不同阈值
            thresholds = np.arange(0.1, 0.9, 0.05)
            for threshold in thresholds:
                preds = (probs > threshold).astype(int)
                if len(np.unique(preds)) > 1:  # 确保有正负预测
                    f1 = f1_score(labels, preds, zero_division=0)
                    if f1 > best_f1:
                        best_f1 = f1
                        best_threshold = threshold
            
            optimal_thresholds[disease] = {
                'threshold': best_threshold,
                'f1': best_f1
            }
            
            print(f"   {disease}: 最优阈值={best_threshold:.3f}, F1={best_f1:.3f}")
        
        return optimal_thresholds
    
    def evaluate_model(self, test_loader):
        """评估模型性能"""
        print(f"🔍 开始模型评估...")
        
        self.model.eval()
        all_probs = []
        all_labels = []
        all_logits = []
        
        with torch.no_grad():
            for batch in tqdm(test_loader, desc="评估中"):
                # 转换数据格式
                patient_data, labels = self.convert_batch_to_patient_data(batch)
                
                # 前向传播
                probs, logits = self.model(patient_data)
                
                all_probs.append(probs.cpu())
                all_labels.append(labels.cpu())
                all_logits.append(logits.cpu())
        
        # 合并结果
        all_probs = torch.cat(all_probs, dim=0).numpy()
        all_labels = torch.cat(all_labels, dim=0).numpy()
        all_logits = torch.cat(all_logits, dim=0).numpy()
        
        print(f"✅ 模型评估完成")
        print(f"   预测概率形状: {all_probs.shape}")
        print(f"   真实标签形状: {all_labels.shape}")
        
        return all_probs, all_labels, all_logits
    
    def calculate_disease_metrics(self, all_probs, all_labels, optimal_thresholds=None):
        """计算每个疾病的详细指标"""
        disease_metrics = {}
        
        print(f"📊 计算各疾病详细指标...")
        
        for i, disease in enumerate(TARGET_DISEASES):
            print(f"\n=== {disease} ===")
            
            if len(TARGET_DISEASES) == 1:
                probs = all_probs.flatten()
                labels = all_labels.flatten()
            else:
                probs = all_probs[:, i]
                labels = all_labels[:, i]
            
            # 使用最优阈值或默认阈值
            if optimal_thresholds and disease in optimal_thresholds:
                threshold = optimal_thresholds[disease]['threshold']
            else:
                threshold = PREDICTION_THRESHOLD
            
            # 计算预测结果
            preds = (probs > threshold).astype(int)
            
            # 基本指标
            accuracy = accuracy_score(labels, preds)
            precision = precision_score(labels, preds, zero_division=0)
            recall = recall_score(labels, preds, zero_division=0)
            f1 = f1_score(labels, preds, zero_division=0)
            
            # AUC
            try:
                auc = roc_auc_score(labels, probs)
            except:
                auc = 0.0
            
            # 混淆矩阵
            tn, fp, fn, tp = confusion_matrix(labels, preds).ravel()
            
            # 医疗评价指标
            sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0.0  # 敏感性
            specificity = tn / (tn + fp) if (tn + fp) > 0 else 0.0  # 特异性
            ppv = tp / (tp + fp) if (tp + fp) > 0 else 0.0  # 阳性预测值
            npv = tn / (tn + fn) if (tn + fn) > 0 else 0.0  # 阴性预测值
            
            # 样本分布
            pred_positive = int(preds.sum())
            pred_negative = len(preds) - pred_positive
            true_positive = int(labels.sum())
            true_negative = len(labels) - true_positive
            
            disease_metrics[disease] = {
                'threshold': threshold,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'auc': auc,
                'sensitivity': sensitivity,
                'specificity': specificity,
                'ppv': ppv,
                'npv': npv,
                'tp': int(tp),
                'tn': int(tn),
                'fp': int(fp),
                'fn': int(fn),
                'pred_positive': pred_positive,
                'pred_negative': pred_negative,
                'true_positive': true_positive,
                'true_negative': true_negative,
                'total_samples': len(labels)
            }
            
            # 打印结果
            print(f"📊 基本指标:")
            print(f"   准确率: {accuracy:.4f}")
            print(f"   精确率: {precision:.4f}")
            print(f"   召回率: {recall:.4f}")
            print(f"   F1分数: {f1:.4f}")
            print(f"   AUC: {auc:.4f}")
            
            print(f"🏥 医疗指标:")
            print(f"   敏感性: {sensitivity:.4f} (真阳性率)")
            print(f"   特异性: {specificity:.4f} (真阴性率)")
            print(f"   阳性预测值: {ppv:.4f} (精确率)")
            print(f"   阴性预测值: {npv:.4f}")
            
            print(f"🎯 混淆矩阵:")
            print(f"   真阳性(TP): {tp}")
            print(f"   真阴性(TN): {tn}")
            print(f"   假阳性(FP): {fp}")
            print(f"   假阴性(FN): {fn}")
            
            print(f"📈 预测分布:")
            print(f"   预测阳性: {pred_positive}/{len(labels)} ({pred_positive/len(labels)*100:.2f}%)")
            print(f"   真实阳性: {true_positive}/{len(labels)} ({true_positive/len(labels)*100:.2f}%)")
            print(f"   使用阈值: {threshold:.3f}")
        
        return disease_metrics
    
    def save_test_results(self, disease_metrics, optimal_thresholds=None):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"test_results_{timestamp}.json"
        
        # 构建完整结果
        test_results = {
            'test_info': {
                'timestamp': timestamp,
                'model_path': MODEL_PATH,
                'test_data_path': DATA_PATH,
                'target_diseases': TARGET_DISEASES,
                'prediction_mode': PREDICTION_MODE,
                'device': str(self.device)
            },
            'disease_metrics': disease_metrics,
            'optimal_thresholds': optimal_thresholds if optimal_thresholds else {},
            'summary_statistics': {
                'total_diseases': len(TARGET_DISEASES),
                'avg_f1': sum(metrics['f1'] for metrics in disease_metrics.values()) / len(disease_metrics),
                'avg_precision': sum(metrics['precision'] for metrics in disease_metrics.values()) / len(disease_metrics),
                'avg_recall': sum(metrics['recall'] for metrics in disease_metrics.values()) / len(disease_metrics),
                'avg_auc': sum(metrics['auc'] for metrics in disease_metrics.values()) / len(disease_metrics),
                'avg_sensitivity': sum(metrics['sensitivity'] for metrics in disease_metrics.values()) / len(disease_metrics),
                'avg_specificity': sum(metrics['specificity'] for metrics in disease_metrics.values()) / len(disease_metrics),
                'best_f1_disease': max(disease_metrics.items(), key=lambda x: x[1]['f1'])[0],
                'worst_f1_disease': min(disease_metrics.items(), key=lambda x: x[1]['f1'])[0]
            }
        }
        
        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(test_results, f, indent=2, ensure_ascii=False)
            print(f"\n💾 测试结果已保存: {results_file}")
        except Exception as e:
            print(f"⚠️ 保存测试结果失败: {e}")
        
        return test_results
    
    def print_summary(self, disease_metrics):
        """打印汇总统计"""
        print(f"\n" + "="*60)
        print(f"📊 测试结果汇总")
        print(f"="*60)
        
        # 汇总统计
        avg_f1 = sum(metrics['f1'] for metrics in disease_metrics.values()) / len(disease_metrics)
        avg_precision = sum(metrics['precision'] for metrics in disease_metrics.values()) / len(disease_metrics)
        avg_recall = sum(metrics['recall'] for metrics in disease_metrics.values()) / len(disease_metrics)
        avg_auc = sum(metrics['auc'] for metrics in disease_metrics.values()) / len(disease_metrics)
        avg_sensitivity = sum(metrics['sensitivity'] for metrics in disease_metrics.values()) / len(disease_metrics)
        avg_specificity = sum(metrics['specificity'] for metrics in disease_metrics.values()) / len(disease_metrics)
        
        print(f"🎯 整体平均指标:")
        print(f"   平均F1分数: {avg_f1:.4f}")
        print(f"   平均精确率: {avg_precision:.4f}")
        print(f"   平均召回率: {avg_recall:.4f}")
        print(f"   平均AUC: {avg_auc:.4f}")
        print(f"   平均敏感性: {avg_sensitivity:.4f}")
        print(f"   平均特异性: {avg_specificity:.4f}")
        
        # 最佳和最差疾病
        best_disease = max(disease_metrics.items(), key=lambda x: x[1]['f1'])
        worst_disease = min(disease_metrics.items(), key=lambda x: x[1]['f1'])
        
        print(f"\n🏆 表现最佳疾病: {best_disease[0]} (F1: {best_disease[1]['f1']:.4f})")
        print(f"⚠️ 表现最差疾病: {worst_disease[0]} (F1: {worst_disease[1]['f1']:.4f})")
        
        # 按F1分数排序
        print(f"\n📈 各疾病F1分数排名:")
        sorted_diseases = sorted(disease_metrics.items(), key=lambda x: x[1]['f1'], reverse=True)
        for i, (disease, metrics) in enumerate(sorted_diseases, 1):
            print(f"   {i:2d}. {disease}: {metrics['f1']:.4f}")
    
    def run_test(self):
        """运行完整测试"""
        print(f"🚀 开始模型测试...")
        
        # 1. 加载模型
        if not self.load_model():
            return False
        
        # 2. 加载测试数据
        test_dataset, test_loader = self.load_test_data()
        if test_dataset is None:
            return False
        
        # 3. 评估模型
        all_probs, all_labels, all_logits = self.evaluate_model(test_loader)
        
        # 4. 寻找最优阈值
        optimal_thresholds = self.find_optimal_threshold_per_disease(all_probs, all_labels)
        
        # 5. 计算详细指标
        disease_metrics = self.calculate_disease_metrics(all_probs, all_labels, optimal_thresholds)
        
        # 6. 打印汇总
        self.print_summary(disease_metrics)
        
        # 7. 保存结果
        test_results = self.save_test_results(disease_metrics, optimal_thresholds)
        
        print(f"\n✅ 测试完成!")
        return True


def main():
    """主函数"""
    print(f"🎯 医疗预测模型测试")
    print(f"="*50)
    
    # 检查文件是否存在
    if not os.path.exists(MODEL_PATH):
        print(f"❌ 模型文件不存在: {MODEL_PATH}")
        return
    
    if not os.path.exists(DATA_PATH):
        print(f"❌ 数据文件不存在: {DATA_PATH}")
        return
    
    # 运行测试
    tester = ModelTester()
    success = tester.run_test()
    
    if success:
        print(f"🎉 测试成功完成!")
    else:
        print(f"❌ 测试失败!")


if __name__ == "__main__":
    main()