#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练性能基准测试脚本
用于测试和比较优化前后的训练性能
"""

import time
import torch
import psutil
import json
from datetime import datetime
from pathlib import Path

# 导入训练模块
from train import SimpleQwenTrainer, load_data_with_cache
from train import (
    TARGET_DISEASES, TEXT_FEATURES, NUMERIC_FEATURES, 
    DATA_PATH, PREDICTION_MODE, BATCH_SIZE, EPOCHS
)


class TrainingBenchmark:
    """训练性能基准测试器"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'system_info': self.get_system_info(),
            'config': self.get_config_info(),
            'benchmarks': {}
        }
    
    def get_system_info(self):
        """获取系统信息"""
        info = {
            'cpu_count': psutil.cpu_count(),
            'memory_gb': psutil.virtual_memory().total / (1024**3),
            'python_version': f"{torch.version.__version__}",
            'cuda_available': torch.cuda.is_available(),
        }
        
        if torch.cuda.is_available():
            info['gpu_count'] = torch.cuda.device_count()
            info['gpu_names'] = [torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())]
            info['cuda_version'] = torch.version.cuda
        
        return info
    
    def get_config_info(self):
        """获取配置信息"""
        return {
            'target_diseases': TARGET_DISEASES,
            'batch_size': BATCH_SIZE,
            'epochs': EPOCHS,
            'data_path': DATA_PATH
        }
    
    def benchmark_data_loading(self, max_samples=1000):
        """基准测试数据加载性能"""
        print("🔄 基准测试: 数据加载性能")
        
        start_time = time.time()
        
        # 加载数据
        train_dataset, val_dataset, test_dataset, scaler, feature_info = load_data_with_cache(
            csv_path=DATA_PATH,
            target_diseases=TARGET_DISEASES,
            prediction_mode=PREDICTION_MODE,
            test_size=0.2,
            max_samples=max_samples,
            time_gap_years=1
        )
        
        loading_time = time.time() - start_time
        
        # 测试DataLoader性能
        from torch.utils.data import DataLoader
        
        # 测试不同worker配置
        worker_configs = [0, 2, 4, 8]
        dataloader_results = {}
        
        for num_workers in worker_configs:
            if train_dataset is None:
                continue
                
            print(f"   测试 {num_workers} workers...")
            
            dataloader = DataLoader(
                train_dataset,
                batch_size=BATCH_SIZE,
                shuffle=True,
                num_workers=num_workers,
                pin_memory=torch.cuda.is_available(),
                prefetch_factor=4 if num_workers > 0 else None,
                persistent_workers=True if num_workers > 0 else False
            )
            
            # 测试前10个batch的加载时间
            start_time = time.time()
            batch_count = 0
            
            for batch in dataloader:
                batch_count += 1
                if batch_count >= 10:
                    break
            
            batch_time = (time.time() - start_time) / batch_count
            batches_per_second = 1.0 / batch_time if batch_time > 0 else 0
            
            dataloader_results[f'workers_{num_workers}'] = {
                'avg_batch_time': batch_time,
                'batches_per_second': batches_per_second
            }
        
        return {
            'data_loading_time': loading_time,
            'dataset_sizes': {
                'train': len(train_dataset) if train_dataset else 0,
                'val': len(val_dataset) if val_dataset else 0,
                'test': len(test_dataset) if test_dataset else 0
            },
            'dataloader_performance': dataloader_results
        }
    
    def benchmark_model_performance(self, max_samples=500, test_epochs=2):
        """基准测试模型训练性能"""
        print("🔄 基准测试: 模型训练性能")
        
        # 加载小数据集进行快速测试
        train_dataset, val_dataset, test_dataset, scaler, feature_info = load_data_with_cache(
            csv_path=DATA_PATH,
            target_diseases=TARGET_DISEASES,
            prediction_mode=PREDICTION_MODE,
            test_size=0.2,
            max_samples=max_samples,
            time_gap_years=1
        )
        
        if train_dataset is None:
            return {'error': 'Failed to load dataset'}
        
        # 创建训练器
        trainer = SimpleQwenTrainer()
        
        # 记录训练开始时间和资源使用
        start_time = time.time()
        start_memory = psutil.virtual_memory().used / (1024**3)
        
        if torch.cuda.is_available():
            start_gpu_memory = torch.cuda.memory_allocated() / (1024**3)
        
        # 临时修改epochs进行快速测试
        original_epochs = trainer.training_history['config']['epochs']
        trainer.training_history['config']['epochs'] = test_epochs
        
        try:
            # 运行训练
            results = trainer.train(train_dataset, val_dataset)
            
            # 记录结束时间和资源使用
            end_time = time.time()
            end_memory = psutil.virtual_memory().used / (1024**3)
            
            training_time = end_time - start_time
            memory_increase = end_memory - start_memory
            
            benchmark_results = {
                'training_time': training_time,
                'memory_increase_gb': memory_increase,
                'epochs_completed': test_epochs,
                'time_per_epoch': training_time / test_epochs,
                'final_metrics': results
            }
            
            if torch.cuda.is_available():
                end_gpu_memory = torch.cuda.memory_allocated() / (1024**3)
                benchmark_results['gpu_memory_increase_gb'] = end_gpu_memory - start_gpu_memory
                benchmark_results['max_gpu_memory_gb'] = torch.cuda.max_memory_allocated() / (1024**3)
            
            return benchmark_results
            
        except Exception as e:
            return {'error': str(e)}
        
        finally:
            # 恢复原始配置
            trainer.training_history['config']['epochs'] = original_epochs
    
    def benchmark_optimization_impact(self):
        """基准测试优化功能的影响"""
        print("🔄 基准测试: 优化功能影响")
        
        optimization_tests = {
            'mixed_precision': {'USE_MIXED_PRECISION': [False, True]},
            'gradient_accumulation': {'GRADIENT_ACCUMULATION_STEPS': [1, 4]},
            'torch_compile': {'USE_TORCH_COMPILE': [False, True]},
            'fused_optimizer': {'USE_FUSED_OPTIMIZER': [False, True]}
        }
        
        results = {}
        
        for test_name, config_changes in optimization_tests.items():
            print(f"   测试 {test_name}...")
            
            test_results = {}
            
            for config_key, values in config_changes.items():
                for value in values:
                    # 这里需要动态修改配置并重新测试
                    # 由于配置是全局变量，这里只记录配置影响
                    test_results[f'{config_key}_{value}'] = {
                        'config': {config_key: value},
                        'note': 'Requires separate test runs with different configs'
                    }
            
            results[test_name] = test_results
        
        return results
    
    def run_full_benchmark(self, max_samples=1000):
        """运行完整的基准测试"""
        print("🚀 开始训练性能基准测试")
        print("=" * 50)
        
        # 数据加载基准测试
        print("\n1. 数据加载性能测试")
        self.results['benchmarks']['data_loading'] = self.benchmark_data_loading(max_samples)
        
        # 模型训练基准测试
        print("\n2. 模型训练性能测试")
        self.results['benchmarks']['model_training'] = self.benchmark_model_performance(
            max_samples=min(500, max_samples), 
            test_epochs=2
        )
        
        # 优化功能影响测试
        print("\n3. 优化功能影响测试")
        self.results['benchmarks']['optimization_impact'] = self.benchmark_optimization_impact()
        
        # 保存结果
        self.save_results()
        
        # 打印总结
        self.print_summary()
    
    def save_results(self):
        """保存基准测试结果"""
        results_dir = Path("benchmark_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"training_benchmark_{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 基准测试结果已保存: {results_file}")
    
    def print_summary(self):
        """打印基准测试总结"""
        print("\n📊 基准测试总结")
        print("=" * 50)
        
        # 系统信息
        sys_info = self.results['system_info']
        print(f"🖥️  系统信息:")
        print(f"   CPU: {sys_info['cpu_count']} 核心")
        print(f"   内存: {sys_info['memory_gb']:.1f} GB")
        if sys_info['cuda_available']:
            print(f"   GPU: {sys_info['gpu_count']} x {sys_info['gpu_names'][0]}")
        
        # 数据加载性能
        if 'data_loading' in self.results['benchmarks']:
            data_perf = self.results['benchmarks']['data_loading']
            print(f"\n📂 数据加载性能:")
            print(f"   数据加载时间: {data_perf['data_loading_time']:.2f}s")
            
            if 'dataloader_performance' in data_perf:
                best_config = max(
                    data_perf['dataloader_performance'].items(),
                    key=lambda x: x[1]['batches_per_second']
                )
                print(f"   最佳DataLoader配置: {best_config[0]}")
                print(f"   最高处理速度: {best_config[1]['batches_per_second']:.2f} batch/s")
        
        # 模型训练性能
        if 'model_training' in self.results['benchmarks']:
            model_perf = self.results['benchmarks']['model_training']
            if 'error' not in model_perf:
                print(f"\n🤖 模型训练性能:")
                print(f"   训练时间: {model_perf['training_time']:.2f}s")
                print(f"   每epoch时间: {model_perf['time_per_epoch']:.2f}s")
                print(f"   内存增长: {model_perf['memory_increase_gb']:.2f} GB")
                if 'gpu_memory_increase_gb' in model_perf:
                    print(f"   GPU内存增长: {model_perf['gpu_memory_increase_gb']:.2f} GB")


def main():
    """主函数"""
    benchmark = TrainingBenchmark()
    
    # 运行基准测试，使用较小的样本数进行快速测试
    benchmark.run_full_benchmark(max_samples=1000)


if __name__ == "__main__":
    main()
