#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的train.py代码功能
验证每种疾病的详细评估是否正常工作
"""

import sys
import os
import torch
import numpy as np
from pathlib import Path

# 添加当前目录到路径
sys.path.append('/root/work_speace/prediction')

# 导入必要模块
from dataset import load_unified_timeseries_data
from train import SimpleQwenTrainer

# 测试配置
TARGET_DISEASES = ['高血压', '脂肪肝', '糖尿病']  # 减少疾病数量以加快测试
DATA_PATH = "/root/work_speace/prediction/coronary_timeseries_data_fixed.csv"
MAX_SAMPLES = 1000  # 使用少量样本进行快速测试
BATCH_SIZE = 16
EPOCHS = 2  # 只训练2个epoch进行测试

def test_disease_metrics():
    """测试每种疾病的详细指标功能"""
    print("🧪 开始测试修改后的train.py功能...")
    print("=" * 50)
    
    # 加载少量测试数据
    print("📂 加载测试数据...")
    try:
        train_dataset, val_dataset, test_dataset, scaler, feature_info = load_unified_timeseries_data(
            csv_path=DATA_PATH,
            target_diseases=TARGET_DISEASES,
            prediction_mode='binary',
            test_size=0.2,
            max_samples=MAX_SAMPLES,
            time_gap_years=1
        )
        
        if train_dataset is None:
            print("❌ 数据加载失败")
            return False
            
        print(f"✅ 数据加载成功:")
        print(f"   训练样本: {len(train_dataset)}")
        print(f"   验证样本: {len(val_dataset)}")
        print(f"   目标疾病: {TARGET_DISEASES}")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False
    
    # 创建训练器
    print("\n🤖 创建训练器...")
    try:
        trainer = SimpleQwenTrainer()
        print("✅ 训练器创建成功")
    except Exception as e:
        print(f"❌ 训练器创建失败: {e}")
        return False
    
    # 测试validate_epoch方法
    print("\n🔍 测试validate_epoch方法...")
    try:
        # 创建简单的数据加载器进行测试
        from torch.utils.data import DataLoader
        val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False)
        
        # 创建模型
        trainer.create_model()
        
        # 运行一次验证
        val_metrics = trainer.validate_epoch(val_loader)
        
        print("✅ validate_epoch测试成功!")
        print(f"   返回的指标键: {list(val_metrics.keys())}")
        
        # 检查是否包含疾病详细指标
        if 'disease_metrics' in val_metrics:
            print(f"✅ 包含疾病详细指标: {list(val_metrics['disease_metrics'].keys())}")
            
            # 打印每种疾病的指标示例
            for disease, metrics in val_metrics['disease_metrics'].items():
                print(f"   {disease}: F1={metrics['f1']:.3f}, 精确率={metrics['precision']:.3f}")
        else:
            print("⚠️ 未检测到疾病详细指标")
            
        return True
        
    except Exception as e:
        print(f"❌ validate_epoch测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_save_results():
    """测试结果保存功能"""
    print("\n💾 测试结果保存功能...")
    
    try:
        # 创建模拟的训练历史数据
        trainer = SimpleQwenTrainer()
        trainer.training_history = {
            'train_losses': [0.5, 0.4, 0.3],
            'val_losses': [0.6, 0.5, 0.4],
            'val_metrics': [
                {
                    'epoch': 1,
                    'f1': 0.1,
                    'auc': 0.6,
                    'tp': 10, 'tn': 100, 'fp': 5, 'fn': 20,
                    'disease_metrics': {
                        '高血压': {'f1': 0.1, 'precision': 0.2, 'recall': 0.05, 'sensitivity': 0.05, 'specificity': 0.95},
                        '脂肪肝': {'f1': 0.0, 'precision': 0.0, 'recall': 0.0, 'sensitivity': 0.0, 'specificity': 1.0},
                        '糖尿病': {'f1': 0.15, 'precision': 0.3, 'recall': 0.1, 'sensitivity': 0.1, 'specificity': 0.9}
                    }
                }
            ],
            'config': {
                'target_diseases': TARGET_DISEASES,
                'qwen_model': 'test_model'
            }
        }
        
        # 测试保存功能
        trainer.save_training_results(0.1, 0.6)
        
        # 检查文件是否生成
        results_dir = Path("/root/work_speace/prediction/results")
        if results_dir.exists():
            result_files = list(results_dir.glob("training_results_*.json"))
            if result_files:
                latest_file = max(result_files, key=lambda x: x.stat().st_mtime)
                print(f"✅ 结果保存成功: {latest_file}")
                
                # 读取并验证文件内容
                import json
                with open(latest_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if 'disease_detailed_metrics' in data:
                    print("✅ 疾病详细指标已保存到JSON文件")
                    print(f"   包含疾病: {list(data['disease_detailed_metrics']['diseases'].keys())}")
                    print(f"   汇总统计: {data['disease_detailed_metrics']['summary_statistics']}")
                else:
                    print("⚠️ JSON文件中未找到疾病详细指标")
                
                return True
            else:
                print("❌ 未找到生成的结果文件")
                return False
        else:
            print("❌ 结果目录不存在")
            return False
            
    except Exception as e:
        print(f"❌ 结果保存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修改后的train.py")
    
    success = True
    
    # 测试1: 疾病指标计算
    if not test_disease_metrics():
        success = False
    
    # 测试2: 结果保存
    if not test_save_results():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 所有测试通过!")
        print("📊 修改后的train.py功能正常:")
        print("   • 支持每种疾病的详细评估")
        print("   • 在训练过程中实时显示疾病指标")
        print("   • 将疾病详细指标保存到JSON文件")
        print("   • 提供疾病指标汇总统计")
    else:
        print("❌ 部分测试失败")
        print("请检查修改的代码是否有问题")
    
    return success

if __name__ == "__main__":
    main()