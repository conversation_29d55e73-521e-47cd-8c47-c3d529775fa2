import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModel, AutoConfig
import math


class MultimodalQwenMedical(nn.Module):
    """多模态Qwen医疗预测模型 - VLA风格架构"""

    def __init__(self,
                 target_diseases,
                 text_features,
                 numeric_features,
                 qwen_model_name='Qwen/Qwen2-7B',
                 prediction_mode='binary'):
        super().__init__()

        self.target_diseases = target_diseases
        self.text_features = text_features
        self.numeric_features = numeric_features
        self.num_diseases = len(target_diseases)
        self.prediction_mode = prediction_mode

        print(f"🤖 多模态Qwen医疗模型初始化:")
        print(f"   Qwen主干: {qwen_model_name}")
        print(f"   目标疾病: {target_diseases}")
        print(f"   文本特征: {text_features}")
        print(f"   数值特征: {numeric_features}")
        print(f"   架构: VLA风格多模态")

        # 1. Qwen tokenizer和配置
        self.tokenizer = AutoTokenizer.from_pretrained(qwen_model_name)
        self.config = AutoConfig.from_pretrained(qwen_model_name)

        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

        # 2. 加载Qwen主干并修改为多模态
        self.qwen = AutoModel.from_pretrained(qwen_model_name)
        self.hidden_size = self.config.hidden_size

        # 3. 数值特征编码器 - 将数值特征映射到token级别
        numeric_dim = len(numeric_features)
        self.numeric_projector = nn.Sequential(
            nn.Linear(numeric_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 512),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(512, self.hidden_size)  # 映射到Qwen的hidden_size
        )

        # 4. 特殊token嵌入
        # 添加特殊的数值特征分隔符token
        self.numeric_start_token = nn.Parameter(
            torch.randn(1, 1, self.hidden_size) * 0.02)
        self.numeric_end_token = nn.Parameter(
            torch.randn(1, 1, self.hidden_size) * 0.02)

        # 5. 位置编码 - 用于数值特征序列
        max_numeric_len = len(numeric_features) + 2  # +2 for start/end tokens
        self.numeric_pos_embedding = nn.Parameter(
            torch.randn(1, max_numeric_len, self.hidden_size) * 0.02)

        # 6. 疾病查询token - 类似VLA的action queries
        self.disease_queries = nn.Parameter(torch.randn(
            self.num_diseases, self.hidden_size) * 0.02)

        # 7. 交叉注意力层 - 让疾病查询关注文本+数值的融合表示
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=self.hidden_size,
            num_heads=self.config.num_attention_heads,
            dropout=0.1,
            batch_first=True
        )

        # 8. 疾病特征解码器 - 将查询结果解码为预测值
        if prediction_mode == 'binary':
            self.disease_decoder = nn.Sequential(
                nn.Linear(self.hidden_size, self.hidden_size // 2),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(self.hidden_size // 2, 1)  # 每个疾病输出一个logit
            )
        else:  # severity
            self.disease_decoder = nn.Sequential(
                nn.Linear(self.hidden_size, self.hidden_size // 2),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(self.hidden_size // 2, 1)  # 每个疾病输出一个严重程度值
            )

        # 9. 输出投影 - 将解码结果映射到最终预测
        self.output_projection = nn.Linear(
            self.num_diseases, self.num_diseases)

        # 疾病权重
        self.disease_weights = nn.Parameter(torch.ones(self.num_diseases))

        print(f"   隐藏维度: {self.hidden_size}")
        print(f"   疾病查询数: {self.num_diseases}")
        print(f"   数值特征维度: {numeric_dim}")

    def prepare_multimodal_input(self, patient_data_list):
        """
        准备多模态输入：文本 + 数值特征
        类似VLA的文本+视觉输入
        """
        batch_size = len(patient_data_list)
        
        # 安全获取设备信息 - 兼容DataParallel模式
        # 在DataParallel模式下，每个副本需要使用自己当前的设备
        try:
            # 首先尝试从embed_tokens获取设备（最可能正确反映当前设备）
            device = self.qwen.embed_tokens.weight.device
        except (AttributeError, StopIteration):
            try:
                device = next(self.parameters()).device
            except StopIteration:
                try:
                    device = next(self.qwen.parameters()).device
                except (StopIteration, AttributeError):
                    # 默认使用cuda:0或cpu
                    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')

        # 1. 准备文本输入
        texts = []
        for patient_data in patient_data_list:
            text_parts = []

            # 构建医疗prompt
            for feature in self.text_features:
                value = patient_data.get(feature, '')
                if value and str(value) not in ['nan', '', '无', '无症状']:
                    if feature == '症状':
                        text_parts.append(f"症状: {value}")
                    elif feature == '既往史':
                        text_parts.append(f"病史: {value}")
                    elif feature == '性别':
                        text_parts.append(f"性别{value}")
                    elif feature == '年龄num':
                        text_parts.append(f"{value}岁患者")
                    else:
                        text_parts.append(f"{feature}: {value}")

            # 构建医疗分析prompt
            if text_parts:
                medical_text = f"医疗分析: {', '.join(text_parts)}。"
            else:
                medical_text = "医疗分析: 患者基本信息。"

            texts.append(medical_text)

        # 2. Tokenize文本
        text_inputs = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=384,  # 留空间给数值特征
            return_tensors='pt'
        )

        # 3. 确保输入张量在正确的设备上（DataParallel兼容）
        input_ids = text_inputs['input_ids'].to(device)
        attention_mask = text_inputs['attention_mask'].to(device)
        
        # 4. 获取文本embedding
        text_embeddings = self.qwen.embed_tokens(input_ids)  # [batch, seq_len, hidden]

        # 4. 准备数值特征embedding
        numeric_data = []
        for patient_data in patient_data_list:
            numeric_row = []
            for feature in self.numeric_features:
                value = patient_data.get(feature, 0)
                try:
                    numeric_value = float(value) if value and str(
                        value) != 'nan' else 0.0
                except:
                    numeric_value = 0.0
                numeric_row.append(numeric_value)
            numeric_data.append(numeric_row)

        numeric_tensor = torch.FloatTensor(
            numeric_data).to(device)  # [batch, numeric_dim]

        # 5. 将数值特征投影到hidden_size
        numeric_projected = self.numeric_projector(
            numeric_tensor)  # [batch, hidden_size]

        # 6. 构建数值特征序列：[START] + numeric_features + [END]
        start_tokens = self.numeric_start_token.expand(
            batch_size, -1, -1)  # [batch, 1, hidden]
        end_tokens = self.numeric_end_token.expand(
            batch_size, -1, -1)    # [batch, 1, hidden]

        # 为每个数值特征创建单独的embedding
        numeric_embeddings = []
        for i in range(len(self.numeric_features)):
            # 取第i个特征值，扩展为embedding
            feature_embedding = numeric_projected.unsqueeze(
                1)  # [batch, 1, hidden]
            # 可以添加特征特定的编码
            feature_embedding = feature_embedding + \
                self.numeric_pos_embedding[:, i:i+1, :]
            numeric_embeddings.append(feature_embedding)

        # 组合数值特征序列
        numeric_sequence = torch.cat(
            [start_tokens] + numeric_embeddings + [end_tokens], dim=1)

        # 7. 拼接文本和数值特征 - 多模态输入
        multimodal_embeddings = torch.cat(
            [text_embeddings, numeric_sequence], dim=1)

        # 8. 构建attention mask
        text_mask = attention_mask  # 使用已经移动到正确设备的attention_mask
        numeric_mask = torch.ones(
            batch_size, numeric_sequence.size(1), device=device)
        multimodal_mask = torch.cat([text_mask, numeric_mask], dim=1)

        return multimodal_embeddings, multimodal_mask

    def forward(self, patient_data_list):
        """
        多模态前向传播 - VLA风格
        """
        batch_size = len(patient_data_list)
        
        # 安全获取设备信息 - 兼容DataParallel模式
        # 在DataParallel模式下，每个副本需要使用自己当前的设备
        try:
            # 首先尝试从embed_tokens获取设备（最可能正确反映当前设备）
            device = self.qwen.embed_tokens.weight.device
        except (AttributeError, StopIteration):
            try:
                device = next(self.parameters()).device
            except StopIteration:
                try:
                    device = next(self.qwen.parameters()).device
                except (StopIteration, AttributeError):
                    # 默认使用cuda:0或cpu
                    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')

        # 1. 准备多模态输入
        multimodal_embeddings, attention_mask = self.prepare_multimodal_input(
            patient_data_list)

        # 2. 通过Qwen处理多模态信息
        # 使用Qwen的标准接口，不要手动调用layers
        qwen_outputs = self.qwen(
            input_ids=None,  # 我们直接传embedding
            inputs_embeds=multimodal_embeddings,
            attention_mask=attention_mask,
            position_ids=None,  # Qwen会自动生成
            past_key_values=None,
            use_cache=False,
            output_attentions=False,
            output_hidden_states=True,
            return_dict=True
        )

        # 获取最后一层的hidden states
        # [batch, seq_len, hidden]
        hidden_states = qwen_outputs.last_hidden_state

        # 4. 疾病查询 - 类似VLA的action prediction
        # 扩展疾病查询到batch维度
        disease_queries = self.disease_queries.unsqueeze(0).expand(
            batch_size, -1, -1)  # [batch, num_diseases, hidden]

        # 5. 交叉注意力 - 疾病查询关注多模态特征
        disease_features, attention_weights = self.cross_attention(
            query=disease_queries,  # [batch, num_diseases, hidden]
            key=hidden_states,      # [batch, seq_len, hidden]
            value=hidden_states,    # [batch, seq_len, hidden]
            key_padding_mask=~attention_mask.bool()  # mask掉padding部分
        )

        # 6. 疾病特征解码 - 将查询结果解码为数值
        # 对每个疾病分别解码
        disease_predictions = []
        for i in range(self.num_diseases):
            disease_feature = disease_features[:, i, :]  # [batch, hidden]
            prediction = self.disease_decoder(disease_feature)  # [batch, 1]
            disease_predictions.append(prediction)

        # 合并所有疾病的预测
        # [batch, num_diseases]
        predictions = torch.cat(disease_predictions, dim=1)

        # 7. 应用疾病权重和输出投影
        weighted_predictions = predictions * self.disease_weights.unsqueeze(0)
        final_predictions = self.output_projection(weighted_predictions)

        # 8. 根据预测模式处理输出
        if self.prediction_mode == 'binary':
            if self.num_diseases == 1:
                # 单疾病二分类
                probs = torch.sigmoid(final_predictions).squeeze(-1)
                return probs, final_predictions.squeeze(-1)
            else:
                # 多疾病二分类
                probs = torch.sigmoid(final_predictions)
                return probs, final_predictions
        else:  # severity
            # 严重程度预测，限制在[0, 5]范围
            clamped_predictions = torch.clamp(final_predictions, 0, 5)
            if self.num_diseases == 1:
                return clamped_predictions.squeeze(-1), final_predictions.squeeze(-1)
            else:
                return clamped_predictions, final_predictions

    def set_disease_weights(self, weights):
        """设置疾病权重"""
        with torch.no_grad():
            self.disease_weights.copy_(
                torch.tensor(weights, dtype=torch.float32))

        print("🎯 设置疾病权重:")
        for disease, weight in zip(self.target_diseases, weights):
            print(f"   {disease}: {weight:.2f}")

    def get_model_info(self):
        """获取模型信息"""
        return {
            'diseases': self.target_diseases,
            'text_features': self.text_features,
            'numeric_features': self.numeric_features,
            'num_diseases': self.num_diseases,
            'mode': self.prediction_mode,
            'hidden_size': self.hidden_size,
            'architecture': 'Multimodal VLA-style'
        }


def create_multimodal_qwen_model(target_diseases, text_features, numeric_features,
                                 qwen_model_name='Qwen/Qwen2-7B', prediction_mode='binary'):
    """
    创建多模态Qwen医疗预测模型
    
    Args:
        target_diseases: 目标疾病列表
        text_features: 文本特征列表
        numeric_features: 数值特征列表
        qwen_model_name: Qwen模型名称
        prediction_mode: 'binary' 或 'severity'
    
    Returns:
        MultimodalQwenMedical实例
    """
    return MultimodalQwenMedical(
        target_diseases=target_diseases,
        text_features=text_features,
        numeric_features=numeric_features,
        qwen_model_name=qwen_model_name,
        prediction_mode=prediction_mode
    )


def calculate_disease_weights_from_data(train_loader, target_diseases):
    """根据训练数据计算疾病权重"""
    num_diseases = len(target_diseases)
    disease_counts = torch.zeros(num_diseases)
    total_samples = 0

    for batch in train_loader:
        labels = batch['label']

        if labels.dim() == 1 and num_diseases == 1:
            disease_counts[0] += labels.sum()
            total_samples += labels.size(0)
        elif labels.dim() == 2:
            disease_counts += labels.sum(dim=0)
            total_samples += labels.size(0)

    positive_rates = disease_counts / total_samples
    max_rate = positive_rates.max()
    weights = (max_rate / (positive_rates + 1e-6)) ** 0.3
    weights = torch.clamp(weights, 0.5, 3.0)

    print(f"📊 疾病统计和权重:")
    for i, (disease, rate, weight) in enumerate(zip(target_diseases, positive_rates, weights)):
        print(f"   {disease}: 阳性率={rate:.3f}, 权重={weight:.2f}")

    return weights.tolist()


if __name__ == "__main__":
    print("🧪 测试多模态Qwen医疗模型...")

    # 配置
    TARGET_DISEASES = ['冠心病']
    TEXT_FEATURES = ['症状', '既往史', '性别', '年龄num']
    NUMERIC_FEATURES = ['体温', '血压', '心率', '血糖']

    print("\n=== VLA风格多模态架构测试 ===")
    model = create_multimodal_qwen_model(
        target_diseases=TARGET_DISEASES,
        text_features=TEXT_FEATURES,
        numeric_features=NUMERIC_FEATURES,
        qwen_model_name='Qwen/Qwen2-0.5B',  # 小模型测试
        prediction_mode='binary'
    )

    # 模拟患者数据
    dummy_patients = [
        {
            '症状': '胸痛心悸',
            '既往史': '高血压',
            '性别': '男',
            '年龄num': 65,
            '体温': 36.5,
            '血压': 150,
            '心率': 85,
            '血糖': 6.2
        }
    ]

    with torch.no_grad():
        probs, logits = model(dummy_patients)

    print(f"✅ VLA风格测试成功!")
    print(f"   输出形状: {probs.shape}")
    print(f"   冠心病概率: {probs.tolist()}")

    print(f"\n🎉 多模态Qwen医疗模型完成!")
    print(f"💡 架构特点:")
    print(f"   ✅ VLA风格多模态架构")
    print(f"   ✅ 文本+数值融合输入Qwen主干")
    print(f"   ✅ 疾病查询+交叉注意力")
    print(f"   ✅ Decoder解码为数值预测")
    print(f"   ✅ 端到端可训练")
