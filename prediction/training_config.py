#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练配置文件 - 集中管理所有训练参数和优化选项
"""

import torch
from pathlib import Path

class TrainingConfig:
    """训练配置类 - 集中管理所有配置参数"""
    
    def __init__(self, config_name="default"):
        self.config_name = config_name
        self._setup_config()
    
    def _setup_config(self):
        """设置配置参数"""
        
        # ==================== 基础配置 ====================
        
        # 🎯 疾病预测配置
        self.TARGET_DISEASES = ['高血压','脂肪肝','糖尿病','高血脂',"支气管炎","气管炎","贫血","肾囊肿",'冠心病']
        
        # 📝 特征选择配置
        self.TEXT_FEATURES = [
            '症状', '既往史', '家族史', '性别', '年龄num',
            '职业', '吸烟状况', '饮酒频率', '饮食习惯'
        ]
        
        self.NUMERIC_FEATURES = [
            '体温', '脉率', '呼吸频率', '左侧收缩压', '左侧舒张压',
            '右侧收缩压', '右侧舒张压', '身高', '体重', '腰围', '体质指数',
            '血红蛋白', '白细胞', '血小板', '空腹血糖MMOL',
            '总胆固醇', '甘油三酯', '血清肌酐', '血尿素'
        ]
        
        # 📂 数据配置
        self.DATA_PATH = "/root/work_speace/prediction/coronary_timeseries_data_fixed.csv"
        self.MAX_SAMPLES = None  # None表示使用全部数据
        self.PREDICTION_MODE = 'binary'
        
        # 💾 数据缓存配置
        self.CACHE_DIR = "/root/work_speace/prediction/data_cache"
        self.USE_CACHE = True
        
        # 🚀 基础训练配置
        self.QWEN_MODEL_NAME = '/root/work_speace/prediction/Qwen2.5-0.5B'
        self.LEARNING_RATE = 1e-5
        self.BATCH_SIZE = 64
        self.EPOCHS = 10
        self.USE_MIXED_PRECISION = True
        
        # ⚖️ 数据不均衡处理策略配置
        self.IMBALANCE_STRATEGY = 'focal_loss'  # 'focal_loss', 'weighted_loss', 'oversample', 'undersample'
        self.FOCAL_ALPHA = 0.1
        self.FOCAL_GAMMA = 3.0
        self.POS_WEIGHT = 200.0
        self.OVERSAMPLE_RATIO = 0.1
        
        # 🎯 预测阈值配置
        self.PREDICTION_THRESHOLD = 0.5
        self.DYNAMIC_THRESHOLD = True
        self.EARLY_STOPPING_PATIENCE = 30
        
        # 🎮 GPU配置
        self.USE_MULTI_GPU = True
        self.NUM_GPUS = 8
        self.CUDA_DEVICE = 0
        
        # ==================== 性能优化配置 ====================
        
        # 🚀 训练优化配置
        self.GRADIENT_ACCUMULATION_STEPS = 4
        self.USE_TORCH_COMPILE = True
        self.USE_GRADIENT_CHECKPOINTING = True
        self.USE_FUSED_OPTIMIZER = True
        
        # 📊 数据加载优化
        self.DATALOADER_NUM_WORKERS = 8
        self.PREFETCH_FACTOR = 4
        self.PIN_MEMORY_DEVICE = "cuda"
        self.PERSISTENT_WORKERS = True
        self.DROP_LAST = True
        
        # 🔧 高级优化配置
        self.GRADIENT_CLIP_NORM = 1.0
        self.WEIGHT_DECAY = 0.01
        self.LR_SCHEDULER = "cosine"  # "cosine", "linear", "constant"
        self.WARMUP_STEPS = 100
        
        # 💾 保存配置
        self.SAVE_PATH = f"qwen_medical_model_optimized.pth"
        self.SAVE_BEST_ONLY = True
        self.SAVE_CHECKPOINT_EVERY = 5  # 每N个epoch保存检查点
        
        # 📊 监控配置
        self.ENABLE_PERFORMANCE_MONITORING = True
        self.LOG_INTERVAL = 10  # 每N个batch记录一次日志
        self.EVAL_INTERVAL = 1   # 每N个epoch评估一次
        
        # 应用配置特定的调整
        self._apply_config_specific_settings()
    
    def _apply_config_specific_settings(self):
        """应用特定配置的设置"""
        
        if self.config_name == "fast_debug":
            # 快速调试配置
            self.MAX_SAMPLES = 1000
            self.EPOCHS = 2
            self.BATCH_SIZE = 32
            self.DATALOADER_NUM_WORKERS = 2
            self.USE_TORCH_COMPILE = False
            self.GRADIENT_ACCUMULATION_STEPS = 1
            
        elif self.config_name == "memory_efficient":
            # 内存高效配置
            self.BATCH_SIZE = 32
            self.GRADIENT_ACCUMULATION_STEPS = 8  # 保持有效批次大小
            self.USE_GRADIENT_CHECKPOINTING = True
            self.DATALOADER_NUM_WORKERS = 4
            self.PREFETCH_FACTOR = 2
            
        elif self.config_name == "high_performance":
            # 高性能配置
            self.BATCH_SIZE = 128
            self.GRADIENT_ACCUMULATION_STEPS = 2
            self.DATALOADER_NUM_WORKERS = 12
            self.PREFETCH_FACTOR = 6
            self.USE_TORCH_COMPILE = True
            self.USE_FUSED_OPTIMIZER = True
            
        elif self.config_name == "single_gpu":
            # 单GPU配置
            self.USE_MULTI_GPU = False
            self.BATCH_SIZE = 32
            self.GRADIENT_ACCUMULATION_STEPS = 4
            self.DATALOADER_NUM_WORKERS = 6
            
        elif self.config_name == "production":
            # 生产环境配置
            self.MAX_SAMPLES = None  # 使用全部数据
            self.EPOCHS = 50
            self.EARLY_STOPPING_PATIENCE = 10
            self.SAVE_CHECKPOINT_EVERY = 2
            self.USE_CACHE = True
    
    def get_effective_batch_size(self):
        """获取有效批次大小"""
        return self.BATCH_SIZE * self.GRADIENT_ACCUMULATION_STEPS
    
    def get_total_steps(self, dataset_size):
        """计算总训练步数"""
        steps_per_epoch = dataset_size // self.get_effective_batch_size()
        return steps_per_epoch * self.EPOCHS
    
    def validate_config(self):
        """验证配置的有效性"""
        errors = []
        warnings = []
        
        # 检查GPU配置
        if self.USE_MULTI_GPU and not torch.cuda.is_available():
            errors.append("多GPU训练需要CUDA支持")
        
        if self.USE_MULTI_GPU and torch.cuda.device_count() < self.NUM_GPUS:
            warnings.append(f"可用GPU数量({torch.cuda.device_count()})少于配置数量({self.NUM_GPUS})")
        
        # 检查内存配置
        if self.BATCH_SIZE * self.GRADIENT_ACCUMULATION_STEPS > 512:
            warnings.append("有效批次大小过大，可能导致内存不足")
        
        # 检查数据加载配置
        if self.DATALOADER_NUM_WORKERS > 16:
            warnings.append("数据加载器工作进程数过多，可能影响性能")
        
        # 检查文件路径
        if not Path(self.DATA_PATH).exists():
            errors.append(f"数据文件不存在: {self.DATA_PATH}")
        
        if not Path(self.QWEN_MODEL_NAME).exists():
            warnings.append(f"模型路径可能不存在: {self.QWEN_MODEL_NAME}")
        
        return errors, warnings
    
    def print_config(self):
        """打印配置信息"""
        print(f"🔧 训练配置: {self.config_name}")
        print("=" * 50)
        
        print(f"📊 数据配置:")
        print(f"   疾病数量: {len(self.TARGET_DISEASES)}")
        print(f"   最大样本: {self.MAX_SAMPLES or '全部'}")
        print(f"   批次大小: {self.BATCH_SIZE}")
        print(f"   有效批次: {self.get_effective_batch_size()}")
        
        print(f"\n🚀 训练配置:")
        print(f"   学习率: {self.LEARNING_RATE}")
        print(f"   训练轮数: {self.EPOCHS}")
        print(f"   混合精度: {self.USE_MIXED_PRECISION}")
        print(f"   梯度累积: {self.GRADIENT_ACCUMULATION_STEPS}")
        
        print(f"\n⚡ 性能优化:")
        print(f"   Torch编译: {self.USE_TORCH_COMPILE}")
        print(f"   梯度检查点: {self.USE_GRADIENT_CHECKPOINTING}")
        print(f"   融合优化器: {self.USE_FUSED_OPTIMIZER}")
        print(f"   数据工作进程: {self.DATALOADER_NUM_WORKERS}")
        
        print(f"\n🎮 硬件配置:")
        print(f"   多GPU训练: {self.USE_MULTI_GPU}")
        if self.USE_MULTI_GPU:
            print(f"   GPU数量: {self.NUM_GPUS}")
        
        # 验证配置
        errors, warnings = self.validate_config()
        
        if errors:
            print(f"\n❌ 配置错误:")
            for error in errors:
                print(f"   - {error}")
        
        if warnings:
            print(f"\n⚠️ 配置警告:")
            for warning in warnings:
                print(f"   - {warning}")
    
    def save_config(self, filepath):
        """保存配置到文件"""
        import json
        
        config_dict = {
            'config_name': self.config_name,
            'target_diseases': self.TARGET_DISEASES,
            'text_features': self.TEXT_FEATURES,
            'numeric_features': self.NUMERIC_FEATURES,
            'data_path': self.DATA_PATH,
            'max_samples': self.MAX_SAMPLES,
            'batch_size': self.BATCH_SIZE,
            'learning_rate': self.LEARNING_RATE,
            'epochs': self.EPOCHS,
            'gradient_accumulation_steps': self.GRADIENT_ACCUMULATION_STEPS,
            'use_mixed_precision': self.USE_MIXED_PRECISION,
            'use_torch_compile': self.USE_TORCH_COMPILE,
            'use_gradient_checkpointing': self.USE_GRADIENT_CHECKPOINTING,
            'dataloader_num_workers': self.DATALOADER_NUM_WORKERS,
            'imbalance_strategy': self.IMBALANCE_STRATEGY
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
        
        print(f"💾 配置已保存到: {filepath}")


# 预定义配置
CONFIGS = {
    "default": TrainingConfig("default"),
    "fast_debug": TrainingConfig("fast_debug"),
    "memory_efficient": TrainingConfig("memory_efficient"),
    "high_performance": TrainingConfig("high_performance"),
    "single_gpu": TrainingConfig("single_gpu"),
    "production": TrainingConfig("production")
}


def get_config(config_name="default"):
    """获取指定的配置"""
    if config_name not in CONFIGS:
        print(f"⚠️ 未知配置: {config_name}，使用默认配置")
        config_name = "default"
    
    return CONFIGS[config_name]


def list_configs():
    """列出所有可用配置"""
    print("📋 可用配置:")
    for name, config in CONFIGS.items():
        print(f"   {name}: {config.__class__.__doc__ or '标准配置'}")


if __name__ == "__main__":
    # 测试配置
    list_configs()
    
    print("\n" + "="*50)
    config = get_config("high_performance")
    config.print_config()
