import torch
from torch.utils.data import Dataset
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from collections import Counter, defaultdict
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')


class UnifiedMedicalDataset(Dataset):
    """统一医疗数据集 - 支持任意疾病组合的时序预测"""

    def __init__(self, text_features, numeric_features, labels, target_diseases, prediction_mode='binary'):
        """
        Args:
            text_features: 文本特征列表
            numeric_features: 数值特征数组 [n_samples, n_features]
            labels: 标签 [n_samples] 或 [n_samples, n_diseases]
            target_diseases: 目标疾病列表
            prediction_mode: 'binary' 或 'severity'
        """
        self.text_features = text_features
        self.numeric_features = torch.FloatTensor(numeric_features)
        self.target_diseases = target_diseases
        self.prediction_mode = prediction_mode

        # 处理标签
        if isinstance(labels, (list, np.ndarray)):
            labels = np.array(labels)

        if len(target_diseases) == 1:
            # 单疾病情况
            if labels.ndim == 2:
                self.labels = torch.FloatTensor(labels[:, 0])  # 取第一列
            else:
                self.labels = torch.FloatTensor(labels)
        else:
            # 多疾病情况
            if labels.ndim == 1:
                # 如果labels是1D，需要扩展为2D
                self.labels = torch.FloatTensor(labels).unsqueeze(
                    1).repeat(1, len(target_diseases))
            else:
                self.labels = torch.FloatTensor(labels)

        print(f"📊 数据集创建: {len(self)} 样本")
        print(f"   目标疾病: {target_diseases}")
        print(f"   标签形状: {self.labels.shape}")
        print(f"   预测模式: {prediction_mode}")

        # 统计标签分布
        self._print_label_distribution()

    def _print_label_distribution(self):
        """打印标签分布"""
        if self.labels.dim() == 1:
            # 单疾病
            pos_count = (self.labels > 0).sum().item()
            total = len(self.labels)
            print(
                f"   {self.target_diseases[0]}: {pos_count}/{total} ({pos_count/total*100:.1f}%)")
        else:
            # 多疾病
            for i, disease in enumerate(self.target_diseases):
                pos_count = (self.labels[:, i] > 0).sum().item()
                total = len(self.labels)
                print(
                    f"   {disease}: {pos_count}/{total} ({pos_count/total*100:.1f}%)")

    def __len__(self):
        return len(self.text_features)

    def __getitem__(self, idx):
        return {
            'text': self.text_features[idx],
            'numeric': self.numeric_features[idx],
            'label': self.labels[idx]
        }

    def get_label_distribution(self):
        """获取标签分布统计"""
        if self.labels.dim() == 1:
            return {
                self.target_diseases[0]: {
                    'positive': (self.labels > 0).sum().item(),
                    'total': len(self.labels),
                    'rate': (self.labels > 0).float().mean().item()
                }
            }
        else:
            stats = {}
            for i, disease in enumerate(self.target_diseases):
                positive = (self.labels[:, i] > 0).sum().item()
                total = len(self.labels)
                stats[disease] = {
                    'positive': positive,
                    'total': total,
                    'rate': positive / total
                }
            return stats


def load_unified_timeseries_data(csv_path, target_diseases, prediction_mode='binary',
                                 test_size=0.2, max_samples=None, time_gap_years=1):
    """
    统一的时序数据加载函数
    
    Args:
        csv_path: CSV文件路径
        target_diseases: 目标疾病列表，如['冠心病'] 或 ['冠心病', '高血压']
        prediction_mode: 'binary' 或 'severity'
        test_size: 测试集比例
        max_samples: 最大样本数限制
        time_gap_years: 时间间隔（年）
    
    Returns:
        train_dataset, val_dataset, test_dataset, scaler, feature_info
    """

    print(f"🔄 加载统一时序数据: {csv_path}")
    print(f"🎯 目标疾病: {target_diseases}")
    print(f"📅 时间间隔: {time_gap_years}年")

    # 预定义特征列表
    NUMERIC_FEATURES = [
        '年龄num', '体温', '脉率', '呼吸频率',
        '左侧收缩压', '左侧舒张压', '右侧收缩压', '右侧舒张压',
        '身高', '体重', '腰围', '体质指数',
        '血红蛋白', '白细胞', '血小板',
        '空腹血糖MMOL', '总胆固醇', '甘油三酯',
        '血清肌酐', '血尿素'
    ]

    TEXT_FEATURES = [
        '症状', '既往史', '家族史', '药物过敏史',
        '地州', '县市', '性别', '职业', '饮食习惯',
        '吸烟状况', '饮酒频率'
    ]

    try:
        # 读取数据
        read_kwargs = {'delimiter': '\t', 'encoding': 'utf-8'}
        if max_samples is not None:
            # 限制读取行数以节省内存
            read_kwargs['nrows'] = max_samples * 10  # 预留额外行数用于筛选
            print(f"🔄 限制读取行数: {read_kwargs['nrows']}")
        
        df = pd.read_csv(csv_path, **read_kwargs)
        print(f"✅ 数据读取成功: {len(df)} 行, {len(df.columns)} 列")

        # 检查必要列是否存在
        required_cols = ['uniq_ID', '年份'] + target_diseases
        missing_cols = [col for col in required_cols if col not in df.columns]

        if missing_cols:
            print(f"❌ 缺少必要列: {missing_cols}")
            available_diseases = [
                col for col in target_diseases if col in df.columns]
            print(f"📋 可用疾病列: {available_diseases}")
            return None, None, None, None, None

        # 分析数据分布
        # print(f"📊 数据概览:")
        year_dist = df['年份'].value_counts().sort_index()
        # print(f"   年份分布: {dict(year_dist)}")

        patient_counts = df['uniq_ID'].value_counts()
        # print(f"   患者总数: {len(patient_counts)}")
        # print(f"   多年数据患者: {(patient_counts >= 2).sum()}")

        # 构建时序对
        print(f"🔗 构建时序预测对...")
        patient_groups = df.groupby('uniq_ID')

        timeseries_pairs = []
        valid_pairs = 0

        for patient_id, group in tqdm(patient_groups, desc="处理患者"):
            group_sorted = group.sort_values('年份')

            for i in range(len(group_sorted) - 1):
                current_data = group_sorted.iloc[i]
                future_data = group_sorted.iloc[i + 1]

                current_year = current_data['年份']
                future_year = future_data['年份']

                # 检查时间间隔
                if future_year - current_year >= time_gap_years:
                    pair = {
                        'patient_id': patient_id,
                        'input_year': current_year,
                        'target_year': future_year,
                        'input_data': current_data,
                        'target_data': future_data
                    }
                    timeseries_pairs.append(pair)
                    valid_pairs += 1

                    if max_samples and valid_pairs >= max_samples:
                        break

            if max_samples and valid_pairs >= max_samples:
                break

        print(f"✅ 时序对构建完成: {valid_pairs} 对")

        if valid_pairs == 0:
            print("❌ 没有有效的时序对")
            return None, None, None, None, None

        # 准备特征和标签
        print(f"📝 准备特征和标签...")

        text_features = []
        numeric_features = []
        labels = []

        for pair in tqdm(timeseries_pairs, desc="提取特征"):
            input_data = pair['input_data']
            target_data = pair['target_data']

            # 提取文本特征
            text_feature = prepare_text_features(input_data, TEXT_FEATURES)
            text_features.append(text_feature)

            # 提取数值特征
            numeric_row = []
            for feature in NUMERIC_FEATURES:
                if feature in input_data:
                    value = pd.to_numeric(input_data[feature], errors='coerce')
                    numeric_row.append(0.0 if pd.isna(value) else float(value))
                else:
                    numeric_row.append(0.0)

            numeric_features.append(numeric_row)

            # 提取标签
            if len(target_diseases) == 1:
                # 单疾病
                label = pd.to_numeric(
                    target_data[target_diseases[0]], errors='coerce')
                labels.append(0.0 if pd.isna(label) else float(label))
            else:
                # 多疾病
                disease_labels = []
                for disease in target_diseases:
                    label = pd.to_numeric(
                        target_data[disease], errors='coerce')
                    disease_labels.append(
                        0.0 if pd.isna(label) else float(label))
                labels.append(disease_labels)

        # 转换为numpy数组
        numeric_features = np.array(numeric_features)
        labels = np.array(labels)

        print(f"✅ 特征提取完成:")
        print(f"   文本特征: {len(text_features)} 条")
        print(f"   数值特征: {numeric_features.shape}")
        print(f"   标签: {labels.shape}")

        # 标准化数值特征
        scaler = StandardScaler()
        numeric_features = scaler.fit_transform(numeric_features)

        # 分析标签分布
        if len(target_diseases) == 1:
            pos_rate = (labels > 0).mean()
            print(f"   {target_diseases[0]} 阳性率: {pos_rate:.3f}")
        else:
            for i, disease in enumerate(target_diseases):
                pos_rate = (labels[:, i] > 0).mean()
                print(f"   {disease} 阳性率: {pos_rate:.3f}")

        # 处理prediction_mode
        if prediction_mode == 'binary':
            if len(target_diseases) == 1:
                final_labels = (labels > 0).astype(float)
            else:
                final_labels = (labels > 0).astype(float)
        else:  # severity
            final_labels = labels

        # 数据集划分
        print(f"📊 划分数据集...")

        # 确保stratify参数正确
        if len(target_diseases) == 1:
            stratify_labels = final_labels
        else:
            # 多标签情况下，使用第一个疾病进行分层
            stratify_labels = final_labels[:,
                                           0] if final_labels.ndim > 1 else final_labels

        # 训练/临时集划分
        train_texts, temp_texts, train_numeric, temp_numeric, train_labels, temp_labels = train_test_split(
            text_features, numeric_features, final_labels,
            test_size=test_size * 2, random_state=42, stratify=stratify_labels
        )

        # 验证/测试集划分
        if len(target_diseases) == 1:
            val_stratify = temp_labels
        else:
            val_stratify = temp_labels[:,
                                       0] if temp_labels.ndim > 1 else temp_labels

        val_texts, test_texts, val_numeric, test_numeric, val_labels, test_labels = train_test_split(
            temp_texts, temp_numeric, temp_labels,
            test_size=0.5, random_state=42, stratify=val_stratify
        )

        # 创建数据集
        train_dataset = UnifiedMedicalDataset(
            train_texts, train_numeric, train_labels, target_diseases, prediction_mode
        )
        val_dataset = UnifiedMedicalDataset(
            val_texts, val_numeric, val_labels, target_diseases, prediction_mode
        )
        test_dataset = UnifiedMedicalDataset(
            test_texts, test_numeric, test_labels, target_diseases, prediction_mode
        )

        # 特征信息
        feature_info = {
            'numeric_features': NUMERIC_FEATURES,
            'text_features': TEXT_FEATURES,
            'target_diseases': target_diseases,
            'numeric_dim': len(NUMERIC_FEATURES),
            'scaler': scaler
        }

        print(f"✅ 数据集创建完成:")
        print(f"   训练集: {len(train_dataset)} 样本")
        print(f"   验证集: {len(val_dataset)} 样本")
        print(f"   测试集: {len(test_dataset)} 样本")

        return train_dataset, val_dataset, test_dataset, scaler, feature_info

    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None, None


def prepare_text_features(row, text_feature_cols):
    """准备文本特征"""
    text_parts = []

    # 基本信息
    age = row.get('年龄num', '')
    gender = row.get('性别', '')
    if pd.notna(age) and str(age) != 'nan':
        text_parts.append(f"年龄: {age}岁")
    if pd.notna(gender) and str(gender) != 'nan':
        text_parts.append(f"性别: {gender}")

    # 症状
    symptoms = row.get('症状', '')
    if pd.notna(symptoms) and str(symptoms) not in ['nan', '无症状', '']:
        text_parts.append(f"症状: {symptoms}")

    # 病史
    for hist_col in ['既往史', '家族史']:
        if hist_col in row:
            history = row.get(hist_col, '')
            if pd.notna(history) and str(history) not in ['nan', '无', '']:
                text_parts.append(f"{hist_col}: {history}")

    # 生活习惯
    for habit_col in ['吸烟状况', '饮酒频率', '饮食习惯']:
        if habit_col in row:
            habit = row.get(habit_col, '')
            if pd.notna(habit) and str(habit) not in ['nan', '从不吸烟', '从不饮酒', '']:
                text_parts.append(f"{habit_col}: {habit}")

    # 职业和地区
    job = row.get('职业', '')
    if pd.notna(job) and str(job) != 'nan':
        text_parts.append(f"职业: {job}")

    city = row.get('地州', '')
    county = row.get('县市', '')
    location_parts = []
    if pd.notna(city) and str(city) != 'nan':
        location_parts.append(str(city))
    if pd.notna(county) and str(county) != 'nan':
        location_parts.append(str(county))
    if location_parts:
        text_parts.append(f"地区: {' '.join(location_parts)}")

    # 药物过敏史
    allergy = row.get('药物过敏史', '')
    if pd.notna(allergy) and str(allergy) not in ['nan', '无', '']:
        text_parts.append(f"药物过敏史: {allergy}")

    # 如果没有任何信息，提供基本描述
    if not text_parts:
        text_parts.append("患者基本信息")

    return " ".join(text_parts)


if __name__ == "__main__":
    print("🧪 测试统一医疗数据集...")

    # 测试数据集创建
    dummy_texts = ["患者症状: 胸痛", "患者症状: 头晕"] * 50
    dummy_numeric = np.random.randn(100, 20)
    dummy_labels = np.random.randint(0, 2, (100, 3))  # 3种疾病

    target_diseases = ['冠心病', '高血压', '糖尿病']

    # 测试多疾病数据集
    dataset_multi = UnifiedMedicalDataset(
        dummy_texts, dummy_numeric, dummy_labels,
        target_diseases, 'binary'
    )

    print(f"✅ 多疾病数据集测试成功: {len(dataset_multi)} 样本")

    # 测试单疾病数据集
    dataset_single = UnifiedMedicalDataset(
        dummy_texts, dummy_numeric, dummy_labels[:, 0],  # 只取第一列
        ['冠心病'], 'binary'
    )

    print(f"✅ 单疾病数据集测试成功: {len(dataset_single)} 样本")

    # 测试数据访问
    sample = dataset_multi[0]
    print(f"✅ 数据访问测试:")
    print(f"   文本: {sample['text'][:50]}...")
    print(f"   数值特征形状: {sample['numeric'].shape}")
    print(f"   标签形状: {sample['label'].shape}")

    print(f"\n🎉 统一医疗数据集测试完成!")
