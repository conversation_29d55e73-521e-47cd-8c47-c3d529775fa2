#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修改后的validate_epoch方法
"""

import torch
import numpy as np
from sklearn.metrics import f1_score, precision_score, recall_score, accuracy_score

def test_disease_metrics_calculation():
    """测试疾病指标计算逻辑"""
    print("🧪 测试疾病指标计算逻辑...")
    
    # 模拟多疾病预测结果
    TARGET_DISEASES = ['高血压', '脂肪肝', '糖尿病']
    batch_size = 100
    
    # 创建模拟数据
    all_probs = torch.rand(batch_size, 3)  # 3种疾病的概率
    all_labels = torch.randint(0, 2, (batch_size, 3)).float()  # 真实标签
    
    # 使用阈值计算预测
    threshold = 0.5
    all_preds = (all_probs > threshold).float()
    
    print(f"✅ 模拟数据创建:")
    print(f"   样本数: {batch_size}")
    print(f"   疾病数: {len(TARGET_DISEASES)}")
    print(f"   预测形状: {all_preds.shape}")
    print(f"   标签形状: {all_labels.shape}")
    
    # 计算每个疾病的详细指标
    disease_metrics = {}
    print(f"\n📊 各疾病详细指标:")
    
    for i, disease in enumerate(TARGET_DISEASES):
        disease_preds = all_preds[:, i].numpy()
        disease_labels = all_labels[:, i].numpy()
        
        disease_f1 = f1_score(disease_labels, disease_preds, zero_division=0)
        disease_precision = precision_score(disease_labels, disease_preds, zero_division=0)
        disease_recall = recall_score(disease_labels, disease_preds, zero_division=0)
        disease_acc = accuracy_score(disease_labels, disease_preds)
        
        disease_pos = int(disease_preds.sum())
        disease_true_pos = int(disease_labels.sum())
        
        # 计算混淆矩阵
        disease_tp = int(((disease_preds == 1) & (disease_labels == 1)).sum())
        disease_tn = int(((disease_preds == 0) & (disease_labels == 0)).sum())
        disease_fp = int(((disease_preds == 1) & (disease_labels == 0)).sum())
        disease_fn = int(((disease_preds == 0) & (disease_labels == 1)).sum())
        
        # 计算医疗指标
        disease_sensitivity = disease_tp / (disease_tp + disease_fn) if (disease_tp + disease_fn) > 0 else 0.0
        disease_specificity = disease_tn / (disease_tn + disease_fp) if (disease_tn + disease_fp) > 0 else 0.0
        
        disease_metrics[disease] = {
            'accuracy': disease_acc,
            'precision': disease_precision,
            'recall': disease_recall,
            'f1': disease_f1,
            'sensitivity': disease_sensitivity,
            'specificity': disease_specificity,
            'pred_positive': disease_pos,
            'true_positive': disease_true_pos,
            'tp': disease_tp,
            'tn': disease_tn,
            'fp': disease_fp,
            'fn': disease_fn
        }
        
        print(f"   {disease}: F1={disease_f1:.3f}, 精确率={disease_precision:.3f}, 召回率={disease_recall:.3f}, 敏感性={disease_sensitivity:.3f}, 特异性={disease_specificity:.3f}")
        print(f"     预测阳性={disease_pos}, 真实阳性={disease_true_pos}, TP={disease_tp}, TN={disease_tn}, FP={disease_fp}, FN={disease_fn}")
    
    # 计算汇总统计
    print(f"\n🎯 疾病指标汇总:")
    avg_f1 = sum(metrics['f1'] for metrics in disease_metrics.values()) / len(disease_metrics)
    avg_precision = sum(metrics['precision'] for metrics in disease_metrics.values()) / len(disease_metrics)
    avg_recall = sum(metrics['recall'] for metrics in disease_metrics.values()) / len(disease_metrics)
    avg_sensitivity = sum(metrics['sensitivity'] for metrics in disease_metrics.values()) / len(disease_metrics)
    avg_specificity = sum(metrics['specificity'] for metrics in disease_metrics.values()) / len(disease_metrics)
    
    best_f1_disease = max(disease_metrics.items(), key=lambda x: x[1]['f1'])[0]
    worst_f1_disease = min(disease_metrics.items(), key=lambda x: x[1]['f1'])[0]
    
    print(f"   平均F1分数: {avg_f1:.3f}")
    print(f"   平均精确率: {avg_precision:.3f}")
    print(f"   平均召回率: {avg_recall:.3f}")
    print(f"   平均敏感性: {avg_sensitivity:.3f}")
    print(f"   平均特异性: {avg_specificity:.3f}")
    print(f"   最佳F1疾病: {best_f1_disease}")
    print(f"   最差F1疾病: {worst_f1_disease}")
    
    return True

def test_json_structure():
    """测试JSON结构"""
    print(f"\n💾 测试JSON结果结构...")
    
    # 模拟疾病指标
    disease_metrics = {
        '高血压': {'f1': 0.3, 'precision': 0.4, 'recall': 0.25, 'sensitivity': 0.25, 'specificity': 0.85},
        '脂肪肝': {'f1': 0.2, 'precision': 0.3, 'recall': 0.15, 'sensitivity': 0.15, 'specificity': 0.9},
        '糖尿病': {'f1': 0.1, 'precision': 0.2, 'recall': 0.07, 'sensitivity': 0.07, 'specificity': 0.95}
    }
    
    # 构建结果结构
    training_results = {
        'disease_detailed_metrics': {
            'description': 'Detailed metrics for each target disease',
            'diseases': disease_metrics,
            'summary_statistics': {
                'avg_f1': sum(metrics['f1'] for metrics in disease_metrics.values()) / len(disease_metrics),
                'avg_precision': sum(metrics['precision'] for metrics in disease_metrics.values()) / len(disease_metrics),
                'avg_recall': sum(metrics['recall'] for metrics in disease_metrics.values()) / len(disease_metrics),
                'avg_sensitivity': sum(metrics['sensitivity'] for metrics in disease_metrics.values()) / len(disease_metrics),
                'avg_specificity': sum(metrics['specificity'] for metrics in disease_metrics.values()) / len(disease_metrics),
                'best_f1_disease': max(disease_metrics.items(), key=lambda x: x[1]['f1'])[0],
                'worst_f1_disease': min(disease_metrics.items(), key=lambda x: x[1]['f1'])[0],
                'total_diseases': len(disease_metrics)
            }
        }
    }
    
    print("✅ JSON结构创建成功:")
    print(f"   包含疾病: {list(training_results['disease_detailed_metrics']['diseases'].keys())}")
    print(f"   汇总统计: {training_results['disease_detailed_metrics']['summary_statistics']}")
    
    # 保存测试文件
    import json
    from pathlib import Path
    
    results_dir = Path("/root/work_speace/prediction/results")
    results_dir.mkdir(exist_ok=True)
    
    test_file = results_dir / "test_disease_metrics.json"
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(training_results, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 测试文件保存: {test_file}")
    
    return True

def main():
    print("🚀 快速功能测试")
    print("=" * 50)
    
    success = True
    
    # 测试1: 疾病指标计算
    if not test_disease_metrics_calculation():
        success = False
    
    # 测试2: JSON结构
    if not test_json_structure():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 快速测试通过!")
        print("🎯 主要新功能:")
        print("   ✅ 多疾病详细指标计算")
        print("   ✅ 实时显示每种疾病的F1、精确率、召回率等")
        print("   ✅ 计算敏感性和特异性等医疗指标")
        print("   ✅ 混淆矩阵详细信息")
        print("   ✅ 疾病指标汇总统计")
        print("   ✅ JSON结果保存结构完整")
    else:
        print("❌ 测试失败")
    
    return success

if __name__ == "__main__":
    main()