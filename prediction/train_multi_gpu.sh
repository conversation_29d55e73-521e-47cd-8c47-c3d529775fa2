#!/bin/bash

# 多GPU训练启动脚本
# 使用方式: bash train_multi_gpu.sh

echo "🚀 启动8GPU分布式训练"
echo "检查GPU状态..."

# 检查GPU数量
GPU_COUNT=$(nvidia-smi --list-gpus | wc -l)
echo "可用GPU数量: $GPU_COUNT"

if [ $GPU_COUNT -lt 8 ]; then
    echo "⚠️ 警告: 可用GPU数量($GPU_COUNT)小于配置的8张GPU"
    echo "将使用所有可用GPU进行训练"
fi

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export MASTER_ADDR=127.0.0.1
export MASTER_PORT=29500

# 使用torchrun启动分布式训练
echo "开始训练..."
torchrun --standalone --nnodes=1 --nproc_per_node=8 train.py

echo "训练完成!"