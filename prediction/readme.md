# 基于Qwen2.5的多模态医疗疾病预测系统

## 项目概述

本项目是一个基于Qwen2.5大语言模型的多模态医疗疾病预测系统，采用VLA（Vision-Language-Action）风格架构，能够处理文本和数值两种模态的医疗数据，实现对多种疾病的时序预测。

### 核心特性

- 🤖 **多模态架构**: 融合文本症状描述和生理指标数值
- 📈 **时序预测**: 基于历史体检数据预测未来疾病风险
- 🎯 **多疾病支持**: 支持同时预测多种疾病（高血压、糖尿病、冠心病等）
- ⚖️ **不均衡处理**: 内置多种策略处理医疗数据的类别不均衡问题
- 📊 **模型对比**: 提供传统机器学习模型对比分析

## 数据规模

- **总样本量**: 3000万条体检记录
- **时间跨度**: 3年连续数据
- **连续样本**: 500万份多年连续体检数据
- **预测模式**: 支持二分类和严重程度预测

## 系统架构

### 1. 多模态Qwen医疗模型 (`model.py`)

#### 核心组件

```python
class MultimodalQwenMedical(nn.Module):
    """多模态Qwen医疗预测模型 - VLA风格架构"""
```

**主要模块：**

1. **Qwen主干网络**: 使用预训练的Qwen2.5模型处理多模态输入
2. **数值特征编码器**: 将生理指标映射到语言模型的隐藏空间
3. **特殊Token嵌入**: 数值特征的开始和结束标记
4. **疾病查询机制**: 类似VLA的action queries，每个疾病对应一个查询向量
5. **交叉注意力层**: 让疾病查询关注融合的文本+数值表示
6. **疾病解码器**: 将查询结果解码为疾病预测值

#### 技术特点

- **VLA风格设计**: 借鉴Vision-Language-Action模型架构
- **端到端训练**: 整个网络可以联合优化
- **灵活预测**: 支持单疾病和多疾病预测
- **模态融合**: 文本和数值特征在Token级别融合

### 2. 统一数据处理 (`dataset.py`)

#### 数据集类

```python
class UnifiedMedicalDataset(Dataset):
    """统一医疗数据集 - 支持任意疾病组合的时序预测"""
```

**功能特性：**

- **时序对构建**: 自动构建历史→未来的预测对
- **多模态特征**: 处理文本症状和数值指标
- **标签分布分析**: 自动统计疾病分布情况
- **灵活配置**: 支持不同时间间隔和疾病组合

#### 特征处理

**文本特征** (包含但不限于)：
- 症状描述、既往史、家族史
- 性别、年龄、职业信息
- 生活习惯（吸烟、饮酒、饮食）
- 地理位置信息

**数值特征** (生理指标)：
- 生命体征：体温、脉率、血压、呼吸频率
- 体格检查：身高、体重、腰围、BMI
- 血液指标：血红蛋白、白细胞、血小板
- 生化指标：血糖、胆固醇、甘油三酯、肌酐等

### 3. 训练系统 (`train.py`)

#### 训练配置

支持多种不均衡数据处理策略：

1. **Focal Loss**: 专门处理极端不均衡数据
   ```python
   FOCAL_ALPHA = 0.1    # 正样本权重
   FOCAL_GAMMA = 3.0    # 难分类样本关注度
   ```

2. **加权损失**: BCE Loss with positive weight
   ```python
   POS_WEIGHT = 200.0   # 正样本权重倍数
   ```

3. **重采样**: 过采样和欠采样策略
   ```python
   OVERSAMPLE_RATIO = 0.1  # 目标正样本比例
   ```

#### 训练特性

- **混合精度训练**: 使用AMP加速训练过程
- **动态阈值**: 自动优化预测阈值以最大化F1分数
- **早停机制**: 防止过拟合
- **模型保存**: 自动保存最佳性能模型

### 4. 机器学习对比 (`ml_comparison.py`)

提供传统机器学习模型的对比分析：

**支持的模型：**
- 逻辑回归、决策树、随机森林
- 梯度提升、SVM、K近邻、朴素贝叶斯
- 多层感知机、XGBoost、LightGBM

**评估指标：**
- 准确率、精确率、召回率、F1分数
- AUC-ROC曲线
- 交叉验证性能

## 使用方法

### 1. 环境配置

```bash
# 安装依赖
pip install torch transformers pandas scikit-learn
pip install xgboost lightgbm  # 可选
```

### 2. 数据准备

数据格式要求（TSV文件）：
- `uniq_ID`: 患者唯一标识
- `年份`: 体检年份
- 疾病列: 目标疾病的标签列
- 特征列: 症状、既往史等文本特征和生理指标

### 3. 模型训练

```python
# 修改配置
TARGET_DISEASES = ['高血压', '糖尿病', '冠心病']  # 目标疾病
DATA_PATH = "your_data.csv"                    # 数据路径
QWEN_MODEL_NAME = "Qwen/Qwen2.5-0.5B"         # 模型路径

# 开始训练
python train.py
```

### 4. 模型对比

```python
# 运行机器学习对比实验
python ml_comparison.py
```

## 技术亮点

### 1. 多模态融合策略

- **Token级融合**: 将数值特征投影到Token空间，与文本Token并行处理
- **位置编码**: 为数值特征序列添加位置信息
- **注意力机制**: 通过交叉注意力让疾病查询关注相关特征

### 2. 疾病查询机制

借鉴VLA模型的action queries概念：
- 每个疾病对应一个可学习的查询向量
- 查询向量通过注意力机制从融合特征中提取相关信息
- 支持多疾病并行预测

### 3. 不均衡数据处理

医疗数据通常存在严重的类别不均衡，系统提供多种解决方案：

- **Focal Loss**: 自动关注难分类样本
- **加权采样**: 平衡正负样本比例
- **阈值优化**: 动态调整预测阈值
- **偏置设置**: 为分类层添加正向偏置

### 4. 时序预测能力

- **时间间隔控制**: 可配置预测的时间跨度
- **历史信息利用**: 基于当前体检结果预测未来风险
- **连续性验证**: 利用多年连续数据提高预测准确性

## 性能评估

### 主要指标

1. **F1 Score**: 主要优化目标，平衡精确率和召回率
2. **AUC-ROC**: 评估模型的区分能力
3. **召回率**: 医疗场景下的关键指标（减少漏诊）
4. **精确率**: 控制误诊率

### 对比分析

系统提供深度学习模型与传统机器学习模型的全面对比：

- **性能对比**: 各模型在相同数据上的表现
- **特征重要性**: 分析哪些特征对预测最重要
- **可视化分析**: ROC曲线、特征重要性图表

## 配置说明

### 关键参数

```python
# 疾病配置
TARGET_DISEASES = ['高血压','脂肪肝','糖尿病']  # 可预测的疾病

# 不均衡处理
IMBALANCE_STRATEGY = 'focal_loss'  # 策略选择
FOCAL_ALPHA = 0.1                  # Focal Loss参数
FOCAL_GAMMA = 3.0

# 预测阈值
PREDICTION_THRESHOLD = 0.5         # 基础阈值
DYNAMIC_THRESHOLD = True           # 是否动态优化

# 训练参数
LEARNING_RATE = 1e-5              # 学习率
BATCH_SIZE = 64                   # 批次大小
EPOCHS = 10                       # 训练轮数
```

## 文件结构

```
project/
├── model.py              # 多模态Qwen模型定义
├── dataset.py            # 统一数据集处理
├── train.py              # 训练脚本
├── ml_comparison.py      # 机器学习对比
├── coronary_timeseries_data_fixed.csv  # 数据文件
└── outputs/              # 结果输出目录
    ├── models/          # 保存的模型
    ├── results/         # 实验结果
    └── plots/           # 可视化图表
```

## 应用场景

### 1. 疾病风险预测
- 基于当前体检结果预测未来疾病风险
- 为体检报告提供风险评估建议
- 辅助医生制定预防策略

### 2. 健康管理
- 个人健康风险监控
- 体检异常指标的预警
- 生活方式改善建议

### 3. 医疗研究
- 大规模流行病学研究
- 疾病发展规律分析
- 预防医学效果评估

## 扩展性

### 1. 疾病扩展
- 支持添加新的疾病预测任务
- 可配置不同疾病的特征权重
- 支持疾病严重程度预测

### 2. 特征扩展
- 易于添加新的生理指标
- 支持医学影像等其他模态数据
- 可整合基因信息等高维特征

### 3. 模型扩展
- 支持不同版本的Qwen模型
- 可替换为其他大语言模型
- 支持模型蒸馏和压缩

## 注意事项

### 1. 数据质量
- 确保医疗数据的准确性和完整性
- 处理缺失值和异常值
- 标准化不同来源的数据格式

### 2. 伦理考虑
- 保护患者隐私信息
- 预测结果仅供参考，不能替代医生诊断
- 遵循相关医疗法规和标准

### 3. 计算资源
- 大模型训练需要足够的GPU资源
- 建议使用分布式训练加速过程
- 考虑使用模型量化减少资源消耗

## 总结

本系统创新性地将大语言模型应用于医疗疾病预测领域，通过多模态架构有效融合文本症状和数值指标，实现了准确的时序疾病预测。系统不仅提供了深度学习解决方案，还包含了完整的传统机器学习对比分析，为医疗AI应用提供了重要参考。

通过合理的数据处理、模型设计和训练策略，系统能够在大规模医疗数据上取得良好的预测性能，为精准医疗和预防医学提供了有力的技术支撑。