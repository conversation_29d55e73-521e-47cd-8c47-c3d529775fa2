#!/usr/bin/env python3
"""
8-GPU DDP训练脚本 - 高性能分布式训练
"""

import os
import sys
import subprocess

def run_ddp_training():
    """启动DDP分布式训练"""
    
    # 训练配置
    script_path = "/root/work_speace/prediction/train.py"
    num_gpus = 8
    
    print(f"🚀 启动8-GPU DDP分布式训练")
    print(f"📊 配置: {num_gpus}张GPU, DistributedDataParallel模式")
    print(f"⚡ 预期性能: 比DataParallel快2-4倍")
    
    # DDP环境变量
    env = os.environ.copy()
    env.update({
        'CUDA_VISIBLE_DEVICES': '0,1,2,3,4,5,6,7',
        'OMP_NUM_THREADS': '4',
        'NCCL_DEBUG': 'INFO'
    })
    
    # torchrun命令
    cmd = [
        'torchrun',
        '--standalone',
        '--nnodes=1',
        f'--nproc_per_node={num_gpus}',
        script_path
    ]
    
    print(f"🔥 执行命令: {' '.join(cmd)}")
    print("=" * 60)
    
    try:
        # 启动DDP训练
        result = subprocess.run(cmd, env=env, check=True)
        print("✅ DDP训练完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ DDP训练失败: {e}")
        return False
    except KeyboardInterrupt:
        print("⏹️ 训练被用户中断")
        return False

if __name__ == "__main__":
    success = run_ddp_training()
    sys.exit(0 if success else 1)