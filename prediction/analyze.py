import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import json
import os
import time
from tqdm import tqdm
import chardet
import csv
import warnings

warnings.filterwarnings('ignore')


class RobustMedicalDataAnalyzer:
    """强化版医疗数据分析器 - 自动检测文件格式"""

    def __init__(self, csv_path, chunk_size=50000):
        self.csv_path = csv_path
        self.chunk_size = chunk_size
        self.delimiter = None
        self.encoding = None
        self.headers = []

        # 存储分析结果
        self.results = {
            'file_info': {},
            'field_analysis': {},
            'disease_analysis': {},
            'high_value_features': {}
        }

    def detect_file_format(self):
        """自动检测文件格式"""
        print("🔍 检测文件格式...")

        # 检测编码
        print("检测文件编码...")
        with open(self.csv_path, 'rb') as f:
            raw_data = f.read(10000)  # 读取前10KB
            encoding_result = chardet.detect(raw_data)
            self.encoding = encoding_result['encoding']
            confidence = encoding_result['confidence']

        print(f"检测到编码: {self.encoding} (置信度: {confidence:.2f})")

        # 如果置信度低，尝试常见编码
        if confidence < 0.7:
            for encoding in ['utf-8', 'gbk', 'gb2312', 'latin-1']:
                try:
                    with open(self.csv_path, 'r', encoding=encoding) as f:
                        f.read(1000)
                    self.encoding = encoding
                    print(f"使用编码: {encoding}")
                    break
                except:
                    continue

        # 检测分隔符
        print("检测CSV分隔符...")
        try:
            with open(self.csv_path, 'r', encoding=self.encoding) as f:
                # 读取前几行
                sample_lines = []
                for i, line in enumerate(f):
                    if i >= 10:  # 只读前10行
                        break
                    sample_lines.append(line)

                # 使用csv.Sniffer检测分隔符
                sniffer = csv.Sniffer()
                sample_text = '\n'.join(sample_lines)
                try:
                    dialect = sniffer.sniff(sample_text, delimiters=',;\t|')
                    self.delimiter = dialect.delimiter
                    print(f"检测到分隔符: '{self.delimiter}'")
                except:
                    # 手动检测分隔符
                    delimiters = [',', ';', '\t', '|']
                    delimiter_counts = {}

                    for delimiter in delimiters:
                        count = sample_text.count(delimiter)
                        delimiter_counts[delimiter] = count

                    # 选择出现次数最多的分隔符
                    self.delimiter = max(delimiter_counts, key=delimiter_counts.get)
                    print(f"手动检测分隔符: '{self.delimiter}' (出现{delimiter_counts[self.delimiter]}次)")

        except Exception as e:
            print(f"分隔符检测失败: {e}")
            self.delimiter = ','  # 默认使用逗号

        return self.encoding, self.delimiter

    def test_read_file(self):
        """测试读取文件"""
        print("🧪 测试文件读取...")

        try:
            # 测试读取前几行
            df_test = pd.read_csv(
                self.csv_path,
                nrows=5,
                delimiter=self.delimiter,
                encoding=self.encoding,
                on_bad_lines='skip'  # 跳过格式错误的行
            )

            self.headers = df_test.columns.tolist()
            print(f"✅ 成功读取! 字段数: {len(self.headers)}")
            print(f"前10个字段: {self.headers[:10]}")

            # 显示数据样例
            print("\n数据样例:")
            print(df_test.head())

            return True

        except Exception as e:
            print(f"❌ 文件读取失败: {e}")

            # 尝试其他方法
            print("尝试使用其他参数...")

            # 尝试不同的参数组合
            param_combinations = [
                {'delimiter': ',', 'encoding': 'utf-8'},
                {'delimiter': ';', 'encoding': 'utf-8'},
                {'delimiter': '\t', 'encoding': 'utf-8'},
                {'delimiter': ',', 'encoding': 'gbk'},
                {'delimiter': ';', 'encoding': 'gbk'},
                {'delimiter': ',', 'encoding': 'latin-1', 'quoting': csv.QUOTE_NONE}
            ]

            for params in param_combinations:
                try:
                    print(f"尝试参数: {params}")
                    df_test = pd.read_csv(
                        self.csv_path,
                        nrows=5,
                        on_bad_lines='skip',
                        **params
                    )

                    if len(df_test.columns) > 10:  # 如果字段数合理
                        self.delimiter = params['delimiter']
                        self.encoding = params['encoding']
                        self.headers = df_test.columns.tolist()
                        print(f"✅ 成功! 字段数: {len(self.headers)}")
                        return True

                except Exception as e2:
                    print(f"   失败: {e2}")
                    continue

            return False

    def get_file_info(self):
        """获取文件基本信息"""
        print("📊 分析文件基本信息...")

        file_size = os.path.getsize(self.csv_path) / (1024 ** 3)  # GB

        # 估算行数
        print("估算总行数...")
        try:
            with open(self.csv_path, 'r', encoding=self.encoding) as f:
                # 统计前1000行的平均长度
                line_lengths = []
                for i, line in enumerate(f):
                    if i >= 1000:
                        break
                    line_lengths.append(len(line.encode(self.encoding)))

                avg_line_length = np.mean(line_lengths)
                estimated_rows = int(file_size * 1024 ** 3 / avg_line_length)

        except:
            estimated_rows = int(file_size * 100000)  # 粗略估算

        self.results['file_info'] = {
            'file_size_gb': file_size,
            'total_fields': len(self.headers),
            'estimated_rows': estimated_rows,
            'encoding': self.encoding,
            'delimiter': repr(self.delimiter)
        }

        print(f"文件大小: {file_size:.2f} GB")
        print(f"字段总数: {len(self.headers)}")
        print(f"估算行数: {estimated_rows:,}")

        return self.results['file_info']

    def identify_disease_fields(self):
        """识别疾病字段"""
        print("🔍 识别疾病字段...")

        disease_fields = []

        # 方法1: 查找标准疾病字段范围
        if '艾滋病' in self.headers and '结核病' in self.headers:
            start_idx = self.headers.index('艾滋病')
            end_idx = self.headers.index('结核病') + 1
            disease_fields = self.headers[start_idx:end_idx]
            print(f"✅ 找到标准疾病字段范围: {len(disease_fields)}个")

        # 方法2: 基于关键词搜索
        else:
            disease_keywords = [
                '高血压', '糖尿病', '脂肪肝', '肝炎', '肾炎', '胃炎', '肺炎',
                '冠心病', '心脏病', '脑梗', '脑出血', '骨质疏松', '贫血',
                '肿瘤', '癌', '白血病', '肺癌', '肝癌', '胃癌',
                '关节炎', '颈椎病', '腰椎病', '白内障', '青光眼'
            ]

            for field in self.headers:
                if any(keyword in field for keyword in disease_keywords):
                    disease_fields.append(field)
                elif any(suffix in field for suffix in ['病', '炎', '症', '癌']):
                    disease_fields.append(field)

        self.disease_fields = disease_fields
        print(f"识别到疾病字段: {len(disease_fields)}个")

        # 显示前20个疾病字段
        if disease_fields:
            print("前20个疾病字段:")
            for i, disease in enumerate(disease_fields[:20]):
                print(f"  {i + 1:2d}. {disease}")

        return disease_fields

    def safe_chunk_analysis(self):
        """安全的分块分析"""
        print("🔄 开始安全分块分析...")

        # 初始化统计
        field_stats = defaultdict(lambda: {'non_null': 0, 'total': 0, 'unique_values': set()})
        disease_stats = defaultdict(Counter)
        high_value_findings = []

        processed_rows = 0
        processed_chunks = 0

        try:
            # 创建chunk reader
            chunk_reader = pd.read_csv(
                self.csv_path,
                chunksize=self.chunk_size,
                delimiter=self.delimiter,
                encoding=self.encoding,
                on_bad_lines='skip'
            )

            # 处理数据块
            for chunk_idx, chunk in enumerate(tqdm(chunk_reader, desc="处理数据块")):
                try:
                    processed_chunks += 1
                    processed_rows += len(chunk)

                    # 分析每个字段
                    for field in chunk.columns:
                        # 基础统计
                        field_stats[field]['total'] += len(chunk)
                        field_stats[field]['non_null'] += chunk[field].notna().sum()

                        # 收集少量唯一值样例
                        if len(field_stats[field]['unique_values']) < 50:
                            try:
                                unique_vals = chunk[field].dropna().unique()[:10]
                                field_stats[field]['unique_values'].update(
                                    str(val) for val in unique_vals
                                )
                            except:
                                pass

                        # 疾病字段特殊分析
                        if field in self.disease_fields:
                            try:
                                value_counts = chunk[field].value_counts()
                                for value, count in value_counts.items():
                                    disease_stats[field][str(value)] += count
                            except:
                                pass

                        # 检查结果字段分析
                        if any(keyword in str(field).lower() for keyword in
                               ['心电图', 'x线', 'b超', '眼底', '检查', '诊断']):
                            try:
                                if chunk[field].dtype == 'object':
                                    abnormal_keywords = ['异常', '阳性', '+', '偏高', '偏低', '增大', '结节', '囊肿']

                                    for keyword in abnormal_keywords:
                                        abnormal_count = chunk[field].astype(str).str.contains(
                                            keyword, case=False, na=False
                                        ).sum()

                                        if abnormal_count > 0:
                                            high_value_findings.append({
                                                'field': field,
                                                'keyword': keyword,
                                                'abnormal_count': abnormal_count,
                                                'chunk_idx': chunk_idx,
                                                'rate': abnormal_count / len(chunk)
                                            })
                            except:
                                pass

                    # 限制处理数量，避免运行太久
                    if processed_chunks >= 1000:  # 处理前1000个块
                        print(f"\n已处理 {processed_chunks} 个数据块，约 {processed_rows:,} 行")
                        print("为节省时间，停止处理更多数据块")
                        break

                except Exception as e:
                    print(f"处理第{chunk_idx}块时出错: {e}")
                    continue

        except Exception as e:
            print(f"读取文件时出错: {e}")
            return None

        # 整理结果
        print("整理分析结果...")

        # 字段分析结果
        self.results['field_analysis'] = {}
        for field, stats in field_stats.items():
            if stats['total'] > 0:
                self.results['field_analysis'][field] = {
                    'completeness': stats['non_null'] / stats['total'],
                    'null_rate': 1 - (stats['non_null'] / stats['total']),
                    'unique_count': len(stats['unique_values']),
                    'sample_values': list(stats['unique_values'])[:10],
                    'total_samples': stats['total']
                }

        # 疾病分析结果
        self.results['disease_analysis'] = {}
        for disease, stats in disease_stats.items():
            self.results['disease_analysis'][disease] = dict(stats)

        # 高价值发现
        self.results['high_value_findings'] = high_value_findings

        self.results['file_info'].update({
            'processed_rows': processed_rows,
            'processed_chunks': processed_chunks
        })

        print(f"✅ 分析完成! 处理了 {processed_rows:,} 行数据")
        return self.results

    def analyze_predictive_potential(self):
        """分析预测潜力"""
        print("🎯 分析疾病预测潜力...")

        predictions = []

        # 分析疾病分布
        for disease, stats in self.results['disease_analysis'].items():
            if isinstance(stats, dict) and stats:
                # 计算患病率
                total_cases = sum(stats.values())
                positive_cases = sum(count for value, count in stats.items()
                                     if str(value) not in ['0', '0.0', 'nan', 'None'])

                if total_cases > 100:  # 至少100个样本
                    prevalence = positive_cases / total_cases

                    # 预测可行性评估
                    if prevalence > 0.01:  # 患病率>1%
                        # 检查相关特征
                        related_features = self._find_related_features(disease)

                        prediction_score = self._calculate_prediction_score(
                            disease, prevalence, related_features
                        )

                        predictions.append({
                            'disease': disease,
                            'prevalence': prevalence,
                            'positive_cases': positive_cases,
                            'total_cases': total_cases,
                            'prediction_score': prediction_score,
                            'related_features': related_features,
                            'recommended': prediction_score > 0.6
                        })

        # 按预测分数排序
        predictions.sort(key=lambda x: x['prediction_score'], reverse=True)

        self.results['high_value_features'] = {
            'predictable_diseases': predictions[:10],
            'top_recommendations': [p for p in predictions[:5] if p['recommended']]
        }

        return predictions

    def _find_related_features(self, disease):
        """查找疾病相关特征"""
        related = []

        # 直接相关特征映射
        feature_mapping = {
            '高血压': ['血压', '收缩压', '舒张压'],
            '糖尿病': ['血糖', '糖化', '胰岛素'],
            '脂肪肝': ['b超', 'B超', '转氨酶', '肝功'],
            '冠心病': ['心电图', '心脏', '胸痛'],
            '肾病': ['肌酐', '尿素', '蛋白尿'],
            '贫血': ['血红蛋白', '红细胞']
        }

        # 查找相关特征
        keywords = feature_mapping.get(disease, [disease.replace('病', '').replace('症', '')])

        for field in self.headers:
            if any(keyword in field for keyword in keywords):
                completeness = self.results['field_analysis'].get(field, {}).get('completeness', 0)
                if completeness > 0.5:  # 完整性>50%
                    related.append({
                        'feature': field,
                        'completeness': completeness,
                        'relevance': 'high' if any(kw in field for kw in keywords[:2]) else 'medium'
                    })

        return related

    def _calculate_prediction_score(self, disease, prevalence, related_features):
        """计算预测分数"""
        score = 0.0

        # 患病率分数 (0.3权重)
        if prevalence > 0.1:  # >10%
            score += 0.3
        elif prevalence > 0.05:  # >5%
            score += 0.2
        elif prevalence > 0.01:  # >1%
            score += 0.1

        # 相关特征分数 (0.7权重)
        if related_features:
            feature_score = 0
            for feature in related_features:
                if feature['relevance'] == 'high':
                    feature_score += 0.3 * feature['completeness']
                else:
                    feature_score += 0.1 * feature['completeness']

            score += min(0.7, feature_score)

        return score

    def generate_summary_report(self, output_path='medical_analysis_summary.json'):
        """生成摘要报告"""
        print("📊 生成摘要报告...")

        # 获取字段完整性统计
        completeness_stats = []
        for field, stats in self.results['field_analysis'].items():
            completeness_stats.append({
                'field': field,
                'completeness': stats['completeness'],
                'total_samples': stats['total_samples']
            })

        completeness_stats.sort(key=lambda x: x['completeness'], reverse=True)

        # 生成报告
        report = {
            'analysis_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'file_info': self.results['file_info'],
            'summary_statistics': {
                'total_fields_analyzed': len(self.results['field_analysis']),
                'disease_fields_found': len(self.disease_fields),
                'high_completeness_fields': len([s for s in completeness_stats if s['completeness'] > 0.8]),
                'predictable_diseases': len(self.results['high_value_features'].get('top_recommendations', []))
            },
            'top_complete_fields': completeness_stats[:20],
            'recommended_diseases': self.results['high_value_features'].get('top_recommendations', []),
            'data_quality_issues': self._identify_quality_issues()
        }

        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)

        print(f"✅ 摘要报告已保存: {output_path}")
        return report

    def _identify_quality_issues(self):
        """识别数据质量问题"""
        issues = []

        # 检查关键字段缺失
        key_fields = ['年龄', '性别', '身高', '体重']
        for field in key_fields:
            if field not in self.headers:
                issues.append(f"缺少关键字段: {field}")

        # 检查字段完整性
        for field, stats in self.results['field_analysis'].items():
            if stats['completeness'] < 0.3 and any(kw in field for kw in ['血压', '血糖', '心电图']):
                issues.append(f"重要字段缺失严重: {field} ({stats['completeness']:.1%})")

        return issues

    def print_analysis_summary(self):
        """打印分析摘要"""
        print("\n" + "=" * 80)
        print("📋 医疗数据分析摘要报告")
        print("=" * 80)

        # 文件信息
        info = self.results['file_info']
        print(f"\n📁 文件信息:")
        print(f"   文件大小: {info['file_size_gb']:.2f} GB")
        print(f"   总字段数: {info['total_fields']}")
        print(f"   处理行数: {info.get('processed_rows', 0):,}")
        print(f"   文件编码: {info['encoding']}")
        print(f"   分隔符: {info['delimiter']}")

        # 疾病分析
        print(f"\n🏥 疾病分析:")
        print(f"   发现疾病字段: {len(self.disease_fields)}个")

        if 'top_recommendations' in self.results['high_value_features']:
            recommendations = self.results['high_value_features']['top_recommendations']
            if recommendations:
                print(f"\n🎯 推荐预测疾病 (TOP 5):")
                for i, rec in enumerate(recommendations[:5]):
                    print(f"   {i + 1}. {rec['disease']}: {rec['positive_cases']}例 "
                          f"({rec['prevalence']:.1%}) - 预测分数: {rec['prediction_score']:.2f}")
            else:
                print("   ⚠️ 未找到合适的预测疾病")

        # 字段完整性
        high_quality_fields = [
            field for field, stats in self.results['field_analysis'].items()
            if stats['completeness'] > 0.8
        ]

        print(f"\n📊 数据质量:")
        print(f"   高完整性字段 (>80%): {len(high_quality_fields)}个")

        if high_quality_fields:
            print("   主要高质量字段:")
            for field in high_quality_fields[:10]:
                completeness = self.results['field_analysis'][field]['completeness']
                print(f"     • {field}: {completeness:.1%}")

        print(f"\n✅ 分析完成! 查看详细报告: medical_analysis_summary.json")


def main():
    """主函数"""
    csv_file = "/root/work_speace/prediction/4-total_3years-uniqPerson_clean.csv"

    print("🚀 强化版医疗数据分析器")
    print(f"目标文件: {csv_file}")

    analyzer = RobustMedicalDataAnalyzer(csv_file, chunk_size=50000)

    try:
        # 步骤1: 检测文件格式
        encoding, delimiter = analyzer.detect_file_format()

        # 步骤2: 测试文件读取
        if not analyzer.test_read_file():
            print("❌ 无法正确读取文件，请检查文件格式")
            return

        # 步骤3: 获取文件信息
        file_info = analyzer.get_file_info()

        # 步骤4: 识别疾病字段
        disease_fields = analyzer.identify_disease_fields()

        # 步骤5: 安全分块分析
        results = analyzer.safe_chunk_analysis()

        if results:
            # 步骤6: 分析预测潜力
            predictions = analyzer.analyze_predictive_potential()

            # 步骤7: 生成报告
            report = analyzer.generate_summary_report()

            # 步骤8: 打印摘要
            analyzer.print_analysis_summary()

    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()