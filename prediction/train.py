# ==================== 配置区域 - 所有参数都在这里修改 ====================

# 🎮 GPU配置
from dataset import load_unified_timeseries_data
from model import create_multimodal_qwen_model
import warnings
from torch.cuda.amp import autocast, GradScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import os
from tqdm import tqdm
import numpy as np
from torch.utils.data import DataLoader, WeightedRandomSampler
import torch.optim as optim
import torch.nn.functional as F
import torch.nn as nn
import torch
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data.distributed import DistributedSampler
from torch.distributed.fsdp import FullyShardedDataParallel as FSDP
from torch.distributed.fsdp.wrap import size_based_auto_wrap_policy
import pickle
import hashlib
from pathlib import Path
import functools
import json
import datetime
import time
import psutil

# 多GPU配置
USE_MULTI_GPU = True  # 是否使用多GPU训练
NUM_GPUS = 8  # 使用的GPU数量
CUDA_DEVICE = 0  # 主GPU编号（分布式训练时会被覆盖）

# 🎯 疾病预测配置
TARGET_DISEASES = ['高血压','脂肪肝','糖尿病','高血脂',"支气管炎","气管炎","贫血","肾囊肿",'冠心病']

# ⚖️ 数据不均衡处理策略配置
# 🔥 选择策略: 'focal_loss', 'weighted_loss', 'oversample', 'undersample', 'adaptive_focal'
IMBALANCE_STRATEGY = 'adaptive_focal'  # 自适应Focal Loss
FOCAL_ALPHA = 0.1        # Focal Loss的alpha参数 (给正样本更高权重)
FOCAL_GAMMA = 3.0        # Focal Loss的gamma参数 (更关注难分类样本)
POS_WEIGHT = 200.0       # 正样本权重倍数（用于weighted_loss）
OVERSAMPLE_RATIO = 0.1   # 过采样后正样本比例（用于oversample）

# 🔧 额外增强选项（在多疾病强不均衡时非常重要）
# - 启用多标签采样器：对任一疾病为阳性的样本增加采样概率
ENABLE_MULTILABEL_SAMPLER = True
# - 多标签采样器的最大正样本权重上限（避免过大导致不稳定）
MULTILABEL_MAX_POS_WEIGHT = 100.0

# 🎯 自适应Focal Loss配置 - 基于训练结果优化参数
ADAPTIVE_FOCAL_CONFIG = {
    'high_prevalence': {'min_rate': 0.08, 'alpha': 0.25, 'gamma': 2.0},    # 高患病率 (>8%) - 提高alpha，降低gamma
    'medium_prevalence': {'min_rate': 0.02, 'alpha': 0.15, 'gamma': 2.5},  # 中患病率 (2-8%) - 大幅提高alpha
    'low_prevalence': {'min_rate': 0.0, 'alpha': 0.10, 'gamma': 3.0}       # 低患病率 (<2%) - 大幅提高alpha，降低gamma
}

# 🎯 预测阈值配置
PREDICTION_THRESHOLD = 0.5  # 🔥 降低预测阈值，从0.5降到0.3
DYNAMIC_THRESHOLD = True    # 动态调整阈值以优化F1
EARLY_STOPPING_PATIENCE = 30  # 早停耐心值

# 🏥 医疗场景优化配置 - 基于训练结果调整
MEDICAL_OPTIMIZATION = {
    'primary_metric': 'f1',             # 主要优化指标：改为F1分数
    'recall_weight': 3.0,               # 大幅提高召回率权重
    'precision_weight': 1.0,            # 精确率权重（减少误诊）
    'specificity_weight': 1.0,          # 降低特异性权重，优先召回率
    'min_recall_threshold': 0.1,        # 降低最低召回率要求
    'max_false_negative_rate': 0.9,     # 放宽假阴性率限制

    # 🔥 召回率惩罚机制配置
    'recall_penalty': {
        'type': 'exponential',          # 惩罚类型：'linear', 'exponential', 'stepped'
        'severe_threshold': 0.2,        # 严重低召回率阈值
        'exponential_base': 0.5,        # 指数惩罚底数
        'step_penalties': [             # 阶梯式惩罚
            {'threshold': 0.1, 'penalty': 0.1},
            {'threshold': 0.2, 'penalty': 0.3},
            {'threshold': 0.3, 'penalty': 0.7}
        ]
    },

    # 🎯 患病率调整策略配置
    'prevalence_adjustment': {
        'enable_dynamic': True,         # 启用动态调整
        'base_threshold': 0.5,          # 基础阈值
        'base_weight': 0.5,             # 基础权重
        'ultra_low_threshold': 0.01,    # 极低患病率阈值（1%）
        'ultra_low_boost': 2.0,         # 极低患病率额外提升
        'disease_severity_weights': {   # 疾病严重程度权重
            'critical': 2.5,            # 致命疾病（如冠心病）
            'serious': 2.0,             # 严重疾病（如糖尿病）
            'moderate': 1.5,            # 中等疾病（如高血压）
            'mild': 1.0                 # 轻度疾病
        }
    }
}

# 🏥 疾病严重程度分类配置
DISEASE_SEVERITY_CLASSIFICATION = {
    '冠心病': 'critical',      # 致命性心血管疾病
    '糖尿病': 'serious',       # 严重慢性疾病
    '高血压': 'moderate',      # 中等慢性疾病
    '脂肪肝': 'moderate',      # 中等代谢疾病
    '高血脂': 'moderate',      # 中等代谢疾病
    '支气管炎': 'mild',        # 轻度呼吸系统疾病
    '气管炎': 'mild',          # 轻度呼吸系统疾病
    '贫血': 'serious',         # 严重血液疾病
    '肾囊肿': 'moderate'       # 中等泌尿系统疾病
}

# 📝 特征选择配置
TEXT_FEATURES = [
    '症状', '既往史', '家族史', '性别', '年龄num',
    '职业', '吸烟状况', '饮酒频率', '饮食习惯'
]

NUMERIC_FEATURES = [
    '体温', '脉率', '呼吸频率', '左侧收缩压', '左侧舒张压',
    '右侧收缩压', '右侧舒张压', '身高', '体重', '腰围', '体质指数',
    '血红蛋白', '白细胞', '血小板', '空腹血糖MMOL',
    '总胆固醇', '甘油三酯', '血清肌酐', '血尿素'
]

# 📂 数据配置
DATA_PATH = "/root/work_speace/prediction/coronary_timeseries_data_fixed.csv"
MAX_SAMPLES = None # 🔥 完整数据集训练，调试时用小数字如1000
PREDICTION_MODE = 'binary'

# 💾 数据缓存配置
CACHE_DIR = "/root/work_speace/prediction/data_cache"
USE_CACHE = True  # 是否使用缓存

# 🚀 训练配置
QWEN_MODEL_NAME = '/root/work_speace/prediction/Qwen2.5-0.5B'
LEARNING_RATE = 2e-5  # 提高学习率，加快收敛
BATCH_SIZE = 128  # 🔥 修复：减少批次大小解决内存问题，8GPU总共64
EPOCHS = 10
USE_MIXED_PRECISION = True

# 🚀 性能优化配置
USE_TORCH_COMPILE = False  # 🔥 修复：禁用torch.compile节省内存
USE_GRADIENT_CHECKPOINTING = True  # 🔥 修复：启用梯度检查点节省内存
DATALOADER_NUM_WORKERS = 4  # 🔥 修复：减少worker数量节省内存
PREFETCH_FACTOR = 2  # 🔥 修复：减少预取因子节省内存
PIN_MEMORY_DEVICE = "cuda"  # pin_memory设备
USE_FUSED_OPTIMIZER = False  # 🔥 修复：禁用融合优化器节省内存

# �💾 保存配置
SAVE_PATH = f"qwen_medical_model_08_16.pth"

# ==================== 训练代码 ====================

warnings.filterwarnings('ignore')

# 设置环境
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'  # 🔥 修复：减少内存碎片
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'  # 同步CUDA操作便于调试

# 导入我们的模块


def setup_distributed_training():
    """初始化分布式训练环境"""
    if not USE_MULTI_GPU:
        return False, 0, 1
    
    # 检查是否有足够的GPU
    if not torch.cuda.is_available():
        print(f"❌ CUDA不可用，无法使用多GPU训练")
        return False, 0, 1
    
    available_gpus = torch.cuda.device_count()
    if available_gpus < NUM_GPUS:
        print(f"⚠️ 可用GPU数量({available_gpus})小于配置数量({NUM_GPUS})")
        actual_gpus = available_gpus
    else:
        actual_gpus = NUM_GPUS
    
    # 检查是否在分布式环境中运行
    if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
        # 分布式训练环境
        rank = int(os.environ['RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
        local_rank = int(os.environ.get('LOCAL_RANK', 0))
        
        print(f"🔗 分布式训练: Rank {rank}/{world_size}, Local Rank {local_rank}")
        
        # 初始化进程组
        dist.init_process_group(backend='nccl')
        torch.cuda.set_device(local_rank)
        
        return True, local_rank, world_size
    else:
        # 单机多卡训练 - 使用DataParallel
        if actual_gpus > 1:
            print(f"🎮 单机多卡训练: 使用 {actual_gpus} 张GPU")
            return "dataparallel", 0, actual_gpus
        else:
            print(f"🎮 单GPU训练")
            return False, 0, 1

def get_data_cache_key(csv_path, target_diseases, prediction_mode, max_samples, time_gap_years, test_size):
    """生成数据缓存的唯一键"""
    # 获取文件修改时间
    file_mtime = os.path.getmtime(csv_path)
    
    # 创建参数字符串
    params_str = f"{csv_path}_{target_diseases}_{prediction_mode}_{max_samples}_{time_gap_years}_{test_size}_{file_mtime}"
    
    # 生成MD5哈希
    cache_key = hashlib.md5(params_str.encode()).hexdigest()
    return cache_key

def save_processed_data(data, cache_path):
    """保存处理好的数据到缓存"""
    print(f"💾 保存数据缓存到: {cache_path}")
    
    # 确保缓存目录存在
    cache_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(cache_path, 'wb') as f:
        pickle.dump(data, f)
    
    print(f"✅ 数据缓存保存成功")

def load_processed_data(cache_path):
    """从缓存加载处理好的数据"""
    if not cache_path.exists():
        return None
        
    print(f"📂 从缓存加载数据: {cache_path}")
    
    try:
        with open(cache_path, 'rb') as f:
            data = pickle.load(f)
        print(f"✅ 数据缓存加载成功")
        return data
    except Exception as e:
        print(f"⚠️ 缓存加载失败: {e}")
        return None

def clear_data_cache():
    """清理所有数据缓存"""
    cache_dir = Path(CACHE_DIR)
    if cache_dir.exists():
        import shutil
        shutil.rmtree(cache_dir)
        print(f"🗑️ 已清理所有数据缓存: {cache_dir}")
    else:
        print(f"📂 缓存目录不存在: {cache_dir}")

def list_cache_files():
    """列出所有缓存文件"""
    cache_dir = Path(CACHE_DIR)
    if not cache_dir.exists():
        print(f"📂 缓存目录不存在: {cache_dir}")
        return
    
    cache_files = list(cache_dir.glob("*.pkl"))
    if not cache_files:
        print(f"📂 缓存目录为空: {cache_dir}")
        return
    
    print(f"📂 缓存文件列表 ({len(cache_files)} 个):")
    total_size = 0
    for cache_file in cache_files:
        size_mb = cache_file.stat().st_size / (1024 * 1024)
        total_size += size_mb
        print(f"   {cache_file.name[:16]}... ({size_mb:.1f} MB)")
    
    print(f"📊 总缓存大小: {total_size:.1f} MB")

def load_data_with_cache(csv_path, target_diseases, prediction_mode='binary',
                        test_size=0.2, max_samples=None, time_gap_years=1):
    """带缓存的数据加载函数"""
    
    if not USE_CACHE:
        print(f"🔄 禁用缓存，直接加载数据...")
        return load_unified_timeseries_data(
            csv_path=csv_path,
            target_diseases=target_diseases,
            prediction_mode=prediction_mode,
            test_size=test_size,
            max_samples=max_samples,
            time_gap_years=time_gap_years
        )
    
    # 生成缓存键和路径
    cache_key = get_data_cache_key(csv_path, target_diseases, prediction_mode, max_samples, time_gap_years, test_size)
    cache_path = Path(CACHE_DIR) / f"{cache_key}.pkl"
    
    print(f"🔍 检查数据缓存: {cache_key[:8]}...")
    
    # 尝试从缓存加载
    cached_data = load_processed_data(cache_path)
    if cached_data is not None:
        print(f"🚀 使用缓存数据，跳过数据处理")
        return cached_data
    
    # 缓存不存在，重新处理数据
    print(f"🔄 缓存不存在，开始处理数据...")
    
    data = load_unified_timeseries_data(
        csv_path=csv_path,
        target_diseases=target_diseases,
        prediction_mode=prediction_mode,
        test_size=test_size,
        max_samples=max_samples,
        time_gap_years=time_gap_years
    )
    
    # 保存到缓存
    if data[0] is not None:  # 确保数据加载成功
        save_processed_data(data, cache_path)
    
    return data


class FocalLoss(nn.Module):
    """Focal Loss - 专门处理极端不均衡数据"""

    def __init__(self, alpha=0.25, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        # inputs: logits, targets: 0/1 labels
        ce_loss = F.binary_cross_entropy_with_logits(
            inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)

        # 计算alpha权重
        alpha_t = self.alpha * targets + (1 - self.alpha) * (1 - targets)

        # Focal Loss公式
        focal_loss = alpha_t * (1 - pt) ** self.gamma * ce_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class WeightedBCELoss(nn.Module):
    """加权BCE Loss"""

    def __init__(self, pos_weight):
        super(WeightedBCELoss, self).__init__()
        self.pos_weight = pos_weight

    def forward(self, inputs, targets):
        # 支持标量或逐病种向量的pos_weight
        if torch.is_tensor(self.pos_weight):
            pw = self.pos_weight.to(device=inputs.device, dtype=inputs.dtype)
        else:
            pw = torch.as_tensor(self.pos_weight, device=inputs.device, dtype=inputs.dtype)

        return F.binary_cross_entropy_with_logits(
            inputs, targets,
            pos_weight=pw
        )


class AdaptiveFocalLoss(nn.Module):
    """自适应Focal Loss - 根据疾病患病率动态调整参数"""

    def __init__(self, target_diseases, disease_prevalence_dict=None):
        super(AdaptiveFocalLoss, self).__init__()
        self.target_diseases = target_diseases
        self.disease_prevalence = disease_prevalence_dict or {}

        # 为每个疾病计算最优的alpha和gamma
        self.disease_params = {}
        for i, disease in enumerate(target_diseases):
            prevalence = self.disease_prevalence.get(disease, 0.05)  # 默认5%
            alpha, gamma, beta = self._calculate_optimal_params(prevalence)
            self.disease_params[i] = {'alpha': alpha, 'gamma': gamma, 'beta': beta}

        print(f"🎯 自适应Focal Loss参数:")
        for i, disease in enumerate(target_diseases):
            params = self.disease_params[i]
            prevalence = self.disease_prevalence.get(disease, 0.05)
            print(f"   {disease} (患病率{prevalence:.1%}): α={params['alpha']:.3f}, γ={params['gamma']:.1f}, β={params['beta']:.1f}")

    def _calculate_optimal_params(self, prevalence):
        """根据患病率计算最优的alpha、gamma和beta参数"""
        config = ADAPTIVE_FOCAL_CONFIG

        if prevalence >= config['high_prevalence']['min_rate']:
            # 高患病率：适中的关注度，轻度控制假阳性
            alpha = config['high_prevalence']['alpha']
            gamma = config['high_prevalence']['gamma']
            beta = 1.1  # 降低特异性调节因子
        elif prevalence >= config['medium_prevalence']['min_rate']:
            # 中患病率：增加关注度，平衡敏感性和特异性
            alpha = config['medium_prevalence']['alpha']
            gamma = config['medium_prevalence']['gamma']
            beta = 1.0  # 不增加特异性惩罚
        else:
            # 低患病率：最高关注度，优先提高召回率
            alpha = config['low_prevalence']['alpha']
            gamma = config['low_prevalence']['gamma']
            beta = 1.0  # 不增加特异性惩罚，优先召回率

        return alpha, gamma, beta

    def forward(self, inputs, targets):
        """前向传播 - 支持多疾病"""
        if len(self.target_diseases) == 1:
            # 单疾病情况
            params = self.disease_params[0]
            return self._focal_loss_single(inputs, targets, params['alpha'], params['gamma'], params['beta'])
        else:
            # 多疾病情况 - 为每个疾病使用不同参数
            total_loss = 0
            for i in range(len(self.target_diseases)):
                params = self.disease_params[i]
                disease_inputs = inputs[:, i] if inputs.dim() > 1 else inputs
                disease_targets = targets[:, i] if targets.dim() > 1 else targets

                loss = self._focal_loss_single(
                    disease_inputs, disease_targets,
                    params['alpha'], params['gamma'], params['beta']
                )
                total_loss += loss

            return total_loss / len(self.target_diseases)

    def _focal_loss_single(self, inputs, targets, alpha, gamma, beta=1.0):
        """单疾病Focal Loss计算 - 增加特异性调节"""
        ce_loss = F.binary_cross_entropy_with_logits(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)

        # 计算alpha权重
        alpha_t = alpha * targets + (1 - alpha) * (1 - targets)

        # 修改为召回率优先的调节：对假阴性（正样本被预测为负）增加惩罚
        # 当targets=1且pt<0.5时（正样本被错误预测为负），增加beta倍惩罚
        recall_penalty = torch.where(
            (targets == 1) & (pt < 0.5),  # 假阴性情况（更危险）
            beta,  # 增加beta倍惩罚
            1.0    # 其他情况正常
        )

        # 增强的Focal Loss公式 - 优先召回率
        focal_loss = alpha_t * (1 - pt) ** gamma * ce_loss * recall_penalty

        return focal_loss.mean()


class MedicalScoreConfig:
    """医疗评分配置管理器"""

    @staticmethod
    def create_config_for_disease_type(disease_type='general'):
        """为特定疾病类型创建优化配置 - 基于测试结果优化特异性"""
        configs = {
            'cardiovascular': {  # 心血管疾病（如冠心病）
                'recall_weight': 3.0,
                'precision_weight': 1.0,
                'specificity_weight': 1.8,  # 提升特异性权重
                'min_recall_threshold': 0.4,
                'min_specificity_threshold': 0.95,  # 新增特异性要求
                'recall_penalty': {'type': 'exponential', 'exponential_base': 0.3},
                'prevalence_adjustment': {'ultra_low_boost': 3.0}
            },
            'metabolic_high_fp': {  # 高假阳性代谢疾病（如高血脂）
                'recall_weight': 2.0,
                'precision_weight': 1.5,
                'specificity_weight': 2.5,  # 最高特异性权重
                'min_recall_threshold': 0.25,
                'min_specificity_threshold': 0.96,
                'recall_penalty': {'type': 'exponential', 'exponential_base': 0.4}
            },
            'metabolic': {  # 代谢疾病（如糖尿病）
                'recall_weight': 2.5,
                'precision_weight': 1.2,
                'specificity_weight': 2.0,  # 提升特异性权重
                'min_recall_threshold': 0.35,
                'min_specificity_threshold': 0.95,
                'recall_penalty': {'type': 'exponential', 'exponential_base': 0.4}
            },
            'chronic_high_prevalence': {  # 高患病率慢性病（如高血压、脂肪肝）
                'recall_weight': 2.0,
                'precision_weight': 1.0,
                'specificity_weight': 2.2,  # 大幅提升特异性权重
                'min_recall_threshold': 0.3,
                'min_specificity_threshold': 0.94,
                'recall_penalty': {'type': 'linear'}
            },
            'chronic': {  # 其他慢性疾病
                'recall_weight': 2.0,
                'precision_weight': 1.0,
                'specificity_weight': 1.8,  # 提升特异性权重
                'min_recall_threshold': 0.3,
                'min_specificity_threshold': 0.95,
                'recall_penalty': {'type': 'linear'}
            },
            'general': {  # 通用配置
                'recall_weight': 2.0,
                'precision_weight': 1.0,
                'specificity_weight': 1.8,  # 提升特异性权重
                'min_recall_threshold': 0.3,
                'min_specificity_threshold': 0.94,
                'recall_penalty': {'type': 'exponential', 'exponential_base': 0.5}
            }
        }

        return configs.get(disease_type, configs['general'])

    @staticmethod
    def update_medical_optimization(disease_type='general'):
        """更新全局医疗优化配置"""
        config = MedicalScoreConfig.create_config_for_disease_type(disease_type)

        # 更新全局配置
        MEDICAL_OPTIMIZATION.update(config)

        print(f"🔧 已更新医疗评分配置为: {disease_type}")
        print(f"   召回率权重: {config['recall_weight']}")
        print(f"   精确率权重: {config['precision_weight']}")
        print(f"   特异性权重: {config['specificity_weight']}")
        print(f"   最低召回率: {config['min_recall_threshold']}")


class PerformanceMonitor:
    """性能监控器 - 跟踪训练性能指标"""

    def __init__(self):
        self.start_time = time.time()
        self.epoch_start_time = None
        self.batch_times = []
        self.gpu_memory_usage = []
        self.cpu_usage = []

    def start_epoch(self):
        """开始一个epoch的监控"""
        self.epoch_start_time = time.time()
        self.batch_times = []

    def record_batch(self, batch_start_time):
        """记录一个batch的处理时间"""
        batch_time = time.time() - batch_start_time
        self.batch_times.append(batch_time)

        # 记录GPU内存使用
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.memory_allocated() / 1024**3  # GB
            self.gpu_memory_usage.append(gpu_memory)

        # 记录CPU使用率
        cpu_percent = psutil.cpu_percent()
        self.cpu_usage.append(cpu_percent)

    def get_epoch_stats(self):
        """获取当前epoch的统计信息"""
        if not self.batch_times:
            return {}

        epoch_time = time.time() - self.epoch_start_time if self.epoch_start_time else 0
        avg_batch_time = sum(self.batch_times) / len(self.batch_times)
        batches_per_second = 1.0 / avg_batch_time if avg_batch_time > 0 else 0

        stats = {
            'epoch_time': epoch_time,
            'avg_batch_time': avg_batch_time,
            'batches_per_second': batches_per_second,
            'total_batches': len(self.batch_times)
        }

        if self.gpu_memory_usage:
            stats['avg_gpu_memory_gb'] = sum(self.gpu_memory_usage) / len(self.gpu_memory_usage)
            stats['max_gpu_memory_gb'] = max(self.gpu_memory_usage)

        if self.cpu_usage:
            stats['avg_cpu_percent'] = sum(self.cpu_usage) / len(self.cpu_usage)

        return stats

    def print_stats(self, epoch):
        """打印性能统计信息"""
        stats = self.get_epoch_stats()
        if stats:
            print(f"⚡ Epoch {epoch+1} 性能统计:")
            print(f"   Epoch时间: {stats['epoch_time']:.1f}s")
            print(f"   平均Batch时间: {stats['avg_batch_time']:.3f}s")
            print(f"   Batch/秒: {stats['batches_per_second']:.1f}")

            if 'avg_gpu_memory_gb' in stats:
                print(f"   GPU内存: {stats['avg_gpu_memory_gb']:.1f}GB (峰值: {stats['max_gpu_memory_gb']:.1f}GB)")

            if 'avg_cpu_percent' in stats:
                print(f"   CPU使用率: {stats['avg_cpu_percent']:.1f}%")


class SimpleQwenTrainer:
    """简化版Qwen医疗预测训练器 - 支持多GPU"""

    def __init__(self):
        # 初始化分布式训练
        self.is_distributed, self.local_rank, self.world_size = setup_distributed_training()
        
        # 初始化训练历史记录
        self.training_history = {
            'train_losses': [],
            'val_losses': [],
            'val_metrics': [],
            'config': {
                'target_diseases': TARGET_DISEASES,
                'qwen_model': QWEN_MODEL_NAME,
                'learning_rate': LEARNING_RATE,
                'batch_size': BATCH_SIZE,
                'epochs': EPOCHS,
                'strategy': IMBALANCE_STRATEGY,
                'threshold': PREDICTION_THRESHOLD
            },
            'best_metrics': {}
        }
        
        # GPU设置
        if self.is_distributed == "dataparallel":
            # DataParallel模式
            self.device = torch.device('cuda:0')
            torch.cuda.set_device(0)
            print(f"🎮 DataParallel模式: 使用 {self.world_size} 张GPU")
            for i in range(self.world_size):
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
        elif self.is_distributed:
            # DistributedDataParallel模式
            self.device = torch.device(f'cuda:{self.local_rank}')
            print(f"🎮 DDP模式: Local Rank {self.local_rank}, Device {torch.cuda.get_device_name(self.local_rank)}")
        else:
            # 单GPU或CPU
            if torch.cuda.is_available() and CUDA_DEVICE < torch.cuda.device_count():
                self.device = torch.device(f'cuda:{CUDA_DEVICE}')
                torch.cuda.set_device(CUDA_DEVICE)
                print(f"🎮 单GPU: {CUDA_DEVICE} ({torch.cuda.get_device_name(CUDA_DEVICE)})")
            else:
                self.device = torch.device('cpu')
                print(f"⚠️ 使用CPU")

        self.scaler = GradScaler() if USE_MIXED_PRECISION else None

        # 性能监控器
        self.performance_monitor = PerformanceMonitor()

        # 只在主进程打印配置
        if not self.is_distributed or self.local_rank == 0:
            print(f"🎯 训练配置:")
            print(f"   疾病: {TARGET_DISEASES}")
            print(f"   模型: {QWEN_MODEL_NAME}")
            print(f"   样本限制: {MAX_SAMPLES}")
            print(f"   批次: {BATCH_SIZE}, 学习率: {LEARNING_RATE}")
            print(f"   不均衡策略: {IMBALANCE_STRATEGY}")
            if self.is_distributed:
                print(f"   多GPU模式: {self.world_size} 张GPU")


    def analyze_data_distribution(self, dataset):
        """分析数据分布 - 支持多疾病"""
        labels = []
        for i in range(len(dataset)):
            label = dataset[i]['label']

            # 检查标签维度
            if label.dim() == 0:  # 标量
                labels.append(label.item())
            elif label.dim() == 1:  # 向量（多疾病）
                labels.append(label.cpu().numpy())  # 转换为numpy数组
            else:
                raise ValueError(f"不支持的标签维度: {label.dim()}")

        # 处理不同情况
        if isinstance(labels[0], (int, float)):
            # 单疾病情况
            labels = np.array(labels)
            pos_count = (labels == 1).sum()
            neg_count = (labels == 0).sum()
            total = len(labels)
            pos_ratio = pos_count / total

            print(f"📊 数据分布分析 (单疾病):")
            print(f"   总样本: {total:,}")
            print(f"   阳性样本: {pos_count:,} ({pos_ratio*100:.3f}%)")
            print(f"   阴性样本: {neg_count:,} ({(1-pos_ratio)*100:.3f}%)")
            print(
                f"   不均衡比例: 1:{neg_count//pos_count if pos_count > 0 else 'inf'}")

            return pos_ratio, labels

        else:
            # 多疾病情况
            labels = np.array(labels)  # [n_samples, n_diseases]
            total = len(labels)

            print(f"📊 数据分布分析 (多疾病):")
            print(f"   总样本: {total:,}")
            print(f"   疾病数量: {len(TARGET_DISEASES)}")

            # 分析每个疾病的分布
            disease_stats = {}
            disease_prevalence = {}
            for i, disease in enumerate(TARGET_DISEASES):
                pos_count = (labels[:, i] == 1).sum()
                neg_count = (labels[:, i] == 0).sum()
                pos_ratio = pos_count / total
                disease_stats[disease] = {
                    'pos_count': pos_count,
                    'neg_count': neg_count,
                    'pos_ratio': pos_ratio
                }
                disease_prevalence[disease] = pos_ratio
                print(f"   {disease}: {pos_count:,}/{total:,} ({pos_ratio*100:.3f}%)")

            # 保存疾病患病率用于自适应损失函数
            self.disease_prevalence = disease_prevalence

            # 返回第一个疾病的正样本比例作为代表（用于分层采样）
            representative_ratio = disease_stats[TARGET_DISEASES[0]]['pos_ratio']

            return representative_ratio, labels


    def create_balanced_sampler(self, labels):
        """创建平衡采样器 - 支持多疾病"""
        if IMBALANCE_STRATEGY not in ['oversample', 'undersample']:
            return None

        print(f"🔄 创建平衡采样器 ({IMBALANCE_STRATEGY}):")

        # 处理多疾病情况 - 使用第一个疾病作为采样依据
        if labels.ndim == 2:
            # 多疾病：使用第一个疾病的标签
            sampling_labels = labels[:, 0]
            print(f"   使用 {TARGET_DISEASES[0]} 作为采样依据")
        else:
            # 单疾病
            sampling_labels = labels

        pos_indices = np.where(sampling_labels == 1)[0]
        neg_indices = np.where(sampling_labels == 0)[0]

        if IMBALANCE_STRATEGY == 'oversample':
            # 过采样：增加正样本权重
            target_pos_count = int(len(labels) * OVERSAMPLE_RATIO)
            oversample_factor = max(1, target_pos_count //
                                    len(pos_indices)) if len(pos_indices) > 0 else 1

            # 计算采样权重
            weights = np.ones(len(labels))
            if len(pos_indices) > 0:
                weights[pos_indices] = oversample_factor

            print(f"   正样本过采样倍数: {oversample_factor}x")
            print(f"   目标正样本比例: {OVERSAMPLE_RATIO*100:.1f}%")

        else:  # undersample
            # 欠采样：减少负样本权重
            target_neg_count = len(
                pos_indices) * 10 if len(pos_indices) > 0 else len(neg_indices)  # 保持1:10的比例
            undersample_factor = max(
                0.1, target_neg_count / len(neg_indices)) if len(neg_indices) > 0 else 1.0

            weights = np.ones(len(labels))
            if len(neg_indices) > 0:
                weights[neg_indices] = undersample_factor

            print(f"   负样本欠采样权重: {undersample_factor:.3f}")

        sampler = WeightedRandomSampler(
            weights=weights,
            num_samples=len(labels),
            replacement=True
        )

        return sampler


    def create_multilabel_balanced_sampler(self, labels):
        """多疾病情况下的加权采样器：对任一阳性样本提升采样权重

        方案：
        - 计算每个疾病的阳性比例p_c，转为权重w_c = (1-p_c)/p_c（上限裁剪）
        - 每个样本的采样权重 = 1 + Σ_c [ I(label_ic==1) * w_c ]
        - 对全阴性的样本，权重为1；出现罕见阳性的样本，权重大幅提升
        """
        if labels.ndim != 2:
            return None

        num_samples, num_classes = labels.shape
        # 计算每类阳性率
        pos_counts = labels.sum(axis=0)
        pos_rates = np.clip(pos_counts / np.maximum(num_samples, 1), 1e-6, 1 - 1e-6)
        class_pos_weights = (1.0 - pos_rates) / pos_rates
        class_pos_weights = np.clip(class_pos_weights, 1.0, MULTILABEL_MAX_POS_WEIGHT)

        # 计算样本权重
        # weights_i = 1 + sum_c (label_ic * w_c)
        weights = 1.0 + (labels * class_pos_weights.reshape(1, -1)).sum(axis=1)

        # 转为tensor（WeightedRandomSampler更稳定）
        weights = torch.as_tensor(weights, dtype=torch.double)
        sampler = WeightedRandomSampler(
            weights=weights,
            num_samples=len(weights),
            replacement=True
        )
        print("🔄 启用多标签采样器: ", end="")
        print("; ".join([f"{d}:w={class_pos_weights[i]:.1f}" for i, d in enumerate(TARGET_DISEASES)]))
        return sampler


    def _search_best_threshold(self, probs_1d, labels_1d):
        """给定单疾病的概率与标签，搜索最佳阈值（加权F1，偏向召回）"""
        disease_prevalence = float(np.mean(labels_1d))
        best_score, best_threshold = 0.0, PREDICTION_THRESHOLD

        if disease_prevalence < 0.01:
            thresholds = np.arange(0.01, 0.4, 0.01); weight_recall = 3.0
        elif disease_prevalence < 0.02:
            thresholds = np.arange(0.05, 0.5, 0.01); weight_recall = 2.0
        elif disease_prevalence < 0.05:
            thresholds = np.arange(0.1, 0.6, 0.01); weight_recall = 1.5
        else:
            thresholds = np.arange(0.1, 0.8, 0.01); weight_recall = 1.0

        for t in thresholds:
            preds = (probs_1d > t).astype(int)
            if preds.min() == preds.max():
                continue
            tp = np.sum((preds == 1) & (labels_1d == 1))
            fp = np.sum((preds == 1) & (labels_1d == 0))
            fn = np.sum((preds == 0) & (labels_1d == 1))
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
            if precision + recall > 0:
                score = (1 + weight_recall**2) * precision * recall / (weight_recall**2 * precision + recall)
            else:
                score = 0.0
            if disease_prevalence < 0.01 and recall < 0.05:
                score *= 0.1
            if score > best_score:
                best_score, best_threshold = score, t
        return best_threshold, best_score, disease_prevalence

    def find_optimal_threshold(self, probs, labels):
        """找到最优预测阈值（单疾病）或各疾病阈值（多疾病）"""
        if not DYNAMIC_THRESHOLD:
            return PREDICTION_THRESHOLD if len(TARGET_DISEASES) == 1 else [PREDICTION_THRESHOLD]*len(TARGET_DISEASES)

        # to numpy
        probs = probs.detach().cpu().numpy() if isinstance(probs, torch.Tensor) else probs
        labels = labels.detach().cpu().numpy() if isinstance(labels, torch.Tensor) else labels

        if probs.ndim == 1 or probs.shape[1] == 1:
            # 单疾病
            th, score, prev = self._search_best_threshold(probs.reshape(-1), labels.reshape(-1))
            print(f"   患病率: {prev:.3f}, 最优阈值: {th:.3f}, 加权F1: {score:.3f}")
            return th

        # 多疾病：分别搜索每个疾病的阈值
        thresholds = []
        details = []
        for i, disease in enumerate(TARGET_DISEASES):
            th, score, prev = self._search_best_threshold(probs[:, i], labels[:, i])
            thresholds.append(th)
            details.append((disease, prev, th, score))
        # 打印摘要
        print("   各疾病最优阈值:")
        for (d, prev, th, sc) in details:
            print(f"     {d}: 患病率={prev:.3f}, 阈值={th:.3f}, 加权F1={sc:.3f}")
        return thresholds

    def calculate_medical_score(self, precision, recall, specificity, prevalence, disease_name=None):
        """计算医疗场景优化评分 - 增强版（特异性优化）"""
        config = MEDICAL_OPTIMIZATION

        # 1. 计算增强的医疗F1分数（包含特异性）
        medical_f1 = self._calculate_enhanced_medical_f1(precision, recall, specificity, config)

        # 2. 计算改进的召回率惩罚
        recall_penalty = self._calculate_recall_penalty(recall, config['recall_penalty'])

        # 3. 计算特异性惩罚（新增）
        specificity_penalty = self._calculate_specificity_penalty(specificity, config)

        # 4. 计算动态患病率调整
        prevalence_boost = self._calculate_prevalence_adjustment(prevalence, disease_name, config['prevalence_adjustment'])

        # 5. 计算最终医疗评分
        medical_score = medical_f1 * recall_penalty * specificity_penalty * prevalence_boost

        # 6. 输出详细信息（仅在主进程）
        if not self.is_distributed or self.local_rank == 0:
            self._log_medical_score_details(
                disease_name, precision, recall, specificity, prevalence,
                medical_f1, recall_penalty, specificity_penalty, prevalence_boost, medical_score
            )

        return medical_score

    def _calculate_enhanced_medical_f1(self, precision, recall, specificity, config):
        """计算增强的医疗F1分数，包含特异性考量"""
        # 权重配置
        recall_weight = config['recall_weight']          # 2.0
        precision_weight = config['precision_weight']    # 1.0
        specificity_weight = config['specificity_weight'] # 1.2

        # 加权指标
        weighted_recall = recall * recall_weight
        weighted_precision = precision * precision_weight
        weighted_specificity = specificity * specificity_weight

        # 计算三元加权调和平均数（类似F1，但包含特异性）
        # Formula: 3 * (WR * WP * WS) / (WR*WP + WR*WS + WP*WS)
        numerator = 3 * weighted_recall * weighted_precision * weighted_specificity
        denominator = (weighted_recall * weighted_precision +
                      weighted_recall * weighted_specificity +
                      weighted_precision * weighted_specificity)

        if denominator > 0:
            enhanced_f1 = numerator / denominator
        else:
            # 退化到传统F1
            enhanced_f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

        return enhanced_f1

    def _calculate_recall_penalty(self, recall, penalty_config):
        """计算改进的召回率惩罚机制"""
        penalty_type = penalty_config['type']
        min_threshold = MEDICAL_OPTIMIZATION['min_recall_threshold']

        if recall >= min_threshold:
            return 1.0  # 无惩罚

        if penalty_type == 'linear':
            # 线性惩罚（原方法）
            return recall / min_threshold

        elif penalty_type == 'exponential':
            # 指数惩罚：对严重低召回率实施更严厉惩罚
            severe_threshold = penalty_config['severe_threshold']
            exponential_base = penalty_config['exponential_base']

            if recall < severe_threshold:
                # 严重低召回率：指数衰减
                penalty_factor = (recall / severe_threshold) ** 2
                return exponential_base * penalty_factor
            else:
                # 中等低召回率：线性衰减
                return recall / min_threshold

        elif penalty_type == 'stepped':
            # 阶梯式惩罚
            step_penalties = penalty_config['step_penalties']

            for step in sorted(step_penalties, key=lambda x: x['threshold']):
                if recall <= step['threshold']:
                    return step['penalty']

            # 如果不在任何阶梯中，使用线性惩罚
            return recall / min_threshold

        else:
            # 默认线性惩罚
            return recall / min_threshold

    def _calculate_specificity_penalty(self, specificity, config):
        """计算特异性惩罚机制 - 基于测试结果优化"""
        min_specificity = config.get('min_specificity_threshold', 0.94)

        if specificity >= min_specificity:
            return 1.0  # 无惩罚

        # 特异性不足的惩罚策略
        if specificity < 0.90:
            # 严重特异性不足：指数惩罚
            penalty_factor = (specificity / 0.90) ** 2
            return 0.3 * penalty_factor
        elif specificity < min_specificity:
            # 中等特异性不足：线性惩罚
            return specificity / min_specificity
        else:
            return 1.0

    def _calculate_prevalence_adjustment(self, prevalence, disease_name, adjustment_config):
        """计算动态患病率调整策略"""
        if not adjustment_config['enable_dynamic']:
            # 使用原始静态调整
            return 1.0 + (1.0 - min(prevalence, 0.5)) * 0.5

        base_threshold = adjustment_config['base_threshold']
        base_weight = adjustment_config['base_weight']
        ultra_low_threshold = adjustment_config['ultra_low_threshold']
        ultra_low_boost = adjustment_config['ultra_low_boost']

        # 1. 基础患病率调整
        base_adjustment = 1.0 + (1.0 - min(prevalence, base_threshold)) * base_weight

        # 2. 极低患病率额外提升
        ultra_low_adjustment = 1.0
        if prevalence < ultra_low_threshold:
            ultra_low_adjustment = ultra_low_boost

        # 3. 疾病严重程度调整
        severity_adjustment = 1.0
        if disease_name and disease_name in DISEASE_SEVERITY_CLASSIFICATION:
            severity_level = DISEASE_SEVERITY_CLASSIFICATION[disease_name]
            severity_weights = adjustment_config['disease_severity_weights']
            severity_adjustment = severity_weights.get(severity_level, 1.0)

        # 4. 综合调整
        total_adjustment = base_adjustment * ultra_low_adjustment * severity_adjustment

        return total_adjustment

    def _log_medical_score_details(self, disease_name, precision, recall, specificity, prevalence,
                                 medical_f1, recall_penalty, specificity_penalty, prevalence_boost, final_score):
        """记录医疗评分的详细计算过程 - 包含特异性惩罚"""
        print(f"\n📊 医疗评分详情 ({disease_name or '总体'}):")
        print(f"   基础指标: P={precision:.3f}, R={recall:.3f}, S={specificity:.3f}")
        print(f"   患病率: {prevalence:.1%}")
        print(f"   增强F1: {medical_f1:.3f}")
        print(f"   召回率惩罚: {recall_penalty:.3f}")
        print(f"   特异性惩罚: {specificity_penalty:.3f}")
        print(f"   患病率提升: {prevalence_boost:.3f}")
        print(f"   最终医疗评分: {final_score:.3f}")

        # 特异性警告
        if specificity < 0.92:
            print(f"   ⚠️ 特异性偏低，假阳性风险较高")

    def get_loss_function(self, disease_prevalence=None):
        """根据策略选择损失函数"""
        if IMBALANCE_STRATEGY == 'focal_loss':
            print(f"📐 使用Focal Loss (alpha={FOCAL_ALPHA}, gamma={FOCAL_GAMMA})")
            return FocalLoss(alpha=FOCAL_ALPHA, gamma=FOCAL_GAMMA)

        elif IMBALANCE_STRATEGY == 'adaptive_focal':
            print(f"🎯 使用自适应Focal Loss")
            return AdaptiveFocalLoss(
                target_diseases=TARGET_DISEASES,
                disease_prevalence_dict=disease_prevalence
            )

        elif IMBALANCE_STRATEGY == 'weighted_loss':
            # 支持为每个疾病设置不同的pos_weight（基于患病率）
            if disease_prevalence and len(TARGET_DISEASES) > 1:
                pos_weights = []
                for d in TARGET_DISEASES:
                    p = max(disease_prevalence.get(d, 1e-6), 1e-6)
                    # pos_weight = neg/pos = (1-p)/p
                    w = (1.0 - p) / p
                    pos_weights.append(min(w, MULTILABEL_MAX_POS_WEIGHT))
                print(f"⚖️ 使用加权BCE Loss (逐病种pos_weight) → {np.round(pos_weights, 2)}")
                return WeightedBCELoss(pos_weight=torch.tensor(pos_weights, dtype=torch.float))
            else:
                print(f"⚖️ 使用加权BCE Loss (pos_weight={POS_WEIGHT})")
                return WeightedBCELoss(pos_weight=POS_WEIGHT)

        else:
            print(f"📊 使用标准BCE Loss + 采样策略")
            return nn.BCEWithLogitsLoss()

    def add_prediction_bias(self):
        """给模型输出层添加正向偏置，鼓励预测阳性"""
        print(f"🎯 添加预测偏置以鼓励阳性预测...")

        # 找到最后的分类层
        if hasattr(self.model, 'classifier'):
            classifier = self.model.classifier
        elif hasattr(self.model, 'disease_decoder'):
            classifier = self.model.disease_decoder
        else:
            print("   ⚠️ 未找到分类层，跳过偏置设置")
            return

        # 为最后一层添加正向偏置
        if isinstance(classifier, nn.Sequential):
            last_layer = classifier[-1]
        else:
            last_layer = classifier

        if hasattr(last_layer, 'bias') and last_layer.bias is not None:
            # 设置更激进的正向偏置，鼓励预测阳性
            with torch.no_grad():
                # 根据训练结果，使用更激进的偏置
                num_diseases = len(TARGET_DISEASES)

                if num_diseases == 1:
                    # 单疾病：设置更大的正向偏置
                    positive_bias = -1.0  # 对应sigmoid约0.27的初始概率
                    last_layer.bias.fill_(positive_bias)
                    print(f"   ✅ 单疾病偏置设置: {positive_bias:.3f}")
                else:
                    # 多疾病：为每个疾病设置差异化偏置
                    for i, disease in enumerate(TARGET_DISEASES):
                        if disease in ['冠心病', '肾囊肿', '贫血']:
                            bias_value = -0.5  # 极低患病率，最大偏置
                        elif disease in ['高血脂', '支气管炎', '气管炎']:
                            bias_value = -1.0  # 低患病率，大偏置
                        elif disease in ['糖尿病']:
                            bias_value = -1.2  # 中低患病率
                        else:
                            bias_value = -1.5  # 高患病率

                        if i < last_layer.bias.size(0):
                            last_layer.bias[i] = bias_value
                            print(f"   ✅ {disease} 偏置: {bias_value:.3f} (概率: {torch.sigmoid(torch.tensor(bias_value)):.3f})")
            print(f"   ✅ 完成激进偏置设置")
        else:
            print(f"   ⚠️ 分类层没有偏置项")

    def create_model(self):
        """创建模型 - 支持多GPU和性能优化"""
        if not self.is_distributed or self.local_rank == 0:
            print(f"🤖 创建模型...")

        # 创建基础模型
        self.base_model = create_multimodal_qwen_model(
            target_diseases=TARGET_DISEASES,
            text_features=TEXT_FEATURES,
            numeric_features=NUMERIC_FEATURES,
            qwen_model_name=QWEN_MODEL_NAME,
            prediction_mode=PREDICTION_MODE
        )

        # 启用梯度检查点节省内存
        if USE_GRADIENT_CHECKPOINTING:
            if hasattr(self.base_model, 'gradient_checkpointing_enable'):
                self.base_model.gradient_checkpointing_enable()
                if not self.is_distributed or self.local_rank == 0:
                    print(f"✅ 启用梯度检查点")

        # 根据分布式模式包装模型
        if self.is_distributed == "dataparallel":
            # DataParallel模式 - 先将模型移动到主GPU
            if not self.is_distributed or self.local_rank == 0:
                print(f"🔄 将模型移动到主GPU: {self.device}")

            # 确保模型完全移动到主GPU
            self.base_model = self._force_model_to_device(self.base_model, self.device)

            # 验证所有参数都在正确设备上
            self._verify_model_device_placement(self.base_model, self.device)

            # 使用DataParallel包装
            device_ids = list(range(self.world_size))
            self.model = nn.DataParallel(self.base_model, device_ids=device_ids)
            if not self.is_distributed or self.local_rank == 0:
                print(f"🔗 使用DataParallel包装模型，GPU设备: {device_ids}")
        elif self.is_distributed:
            # 使用传统DDP，更稳定
            if not self.is_distributed or self.local_rank == 0:
                print(f"🔄 使用DDP包装模型...")

            # 将模型移动到当前GPU
            self.base_model = self._force_model_to_device(self.base_model, self.device)

            # 使用DDP包装
            self.model = DDP(
                self.base_model,
                device_ids=[self.local_rank],
                output_device=self.local_rank,
                find_unused_parameters=False  # 性能优化
            )

            if self.local_rank == 0:
                print(f"✅ DDP包装完成")
        else:
            # 单GPU模式
            self.base_model = self._force_model_to_device(self.base_model, self.device)
            self.model = self.base_model

        # 使用torch.compile优化模型
        if USE_TORCH_COMPILE and hasattr(torch, 'compile'):
            try:
                self.model = torch.compile(self.model, mode="reduce-overhead")
                if not self.is_distributed or self.local_rank == 0:
                    print(f"🚀 启用torch.compile优化")
            except Exception as e:
                if not self.is_distributed or self.local_rank == 0:
                    print(f"⚠️ torch.compile失败: {e}")

        # 在模型包装完成后添加预测偏置
        self.add_prediction_bias()

        # 优化器 - 使用融合优化器提升性能
        if USE_FUSED_OPTIMIZER and torch.cuda.is_available():
            try:
                self.optimizer = optim.AdamW(
                    self.model.parameters(),
                    lr=LEARNING_RATE,
                    weight_decay=0.01,
                    fused=True  # 融合优化器
                )
                if not self.is_distributed or self.local_rank == 0:
                    print(f"🚀 使用融合AdamW优化器")
            except:
                self.optimizer = optim.AdamW(
                    self.model.parameters(),
                    lr=LEARNING_RATE,
                    weight_decay=0.01
                )
                if not self.is_distributed or self.local_rank == 0:
                    print(f"⚠️ 融合优化器不可用，使用标准AdamW")
        else:
            self.optimizer = optim.AdamW(
                self.model.parameters(),
                lr=LEARNING_RATE,
                weight_decay=0.01
            )

        # 学习率调度器将在train方法中初始化（需要知道数据集大小）
        self.scheduler = None

        # 损失函数 - 传递疾病患病率（如果可用）
        disease_prevalence = getattr(self, 'disease_prevalence', None)
        self.criterion = self.get_loss_function(disease_prevalence)

        if not self.is_distributed or self.local_rank == 0:
            print(f"✅ 模型创建完成")

    def _force_model_to_device(self, model, target_device):
        """强制将模型的所有组件移动到目标设备"""
        if not self.is_distributed or self.local_rank == 0:
            print(f"🔧 强制移动模型到设备: {target_device}")

        # 基本的 .to() 调用 - 这应该处理大部分情况
        model = model.to(target_device)

        # 特别处理Qwen模型的embed_tokens层（经常是设备不一致的源头）
        if hasattr(model, 'qwen') and hasattr(model.qwen, 'embed_tokens'):
            model.qwen.embed_tokens = model.qwen.embed_tokens.to(target_device)
            if not self.is_distributed or self.local_rank == 0:
                print(f"   ✅ 移动embed_tokens到 {target_device}")

        # 确保所有Parameter都在正确设备上
        device_mismatches = 0
        for name, param in model.named_parameters():
            if param.device != target_device:
                try:
                    param.data = param.data.to(target_device)
                    if param.grad is not None:
                        param.grad = param.grad.to(target_device)
                    device_mismatches += 1
                except Exception as e:
                    if not self.is_distributed or self.local_rank == 0:
                        print(f"⚠️ 无法移动参数 {name}: {e}")

        # 确保所有Buffer都在正确设备上
        for name, buffer in model.named_buffers():
            if buffer.device != target_device:
                try:
                    buffer.data = buffer.data.to(target_device)
                    device_mismatches += 1
                except Exception as e:
                    if not self.is_distributed or self.local_rank == 0:
                        print(f"⚠️ 无法移动缓冲区 {name}: {e}")

        if not self.is_distributed or self.local_rank == 0:
            if device_mismatches > 0:
                print(f"   📍 修正了 {device_mismatches} 个设备不匹配")
            else:
                print(f"   ✅ 所有组件都在 {target_device}")

        return model

    def _verify_model_device_placement(self, model, target_device):
        """验证模型的所有参数和缓冲区都在目标设备上"""
        device_mismatches = []

        # 检查所有参数
        for name, param in model.named_parameters():
            if param.device != target_device:
                device_mismatches.append(f"参数 {name}: {param.device} != {target_device}")

        # 检查所有缓冲区
        for name, buffer in model.named_buffers():
            if buffer.device != target_device:
                device_mismatches.append(f"缓冲区 {name}: {buffer.device} != {target_device}")

        if device_mismatches:
            print(f"⚠️ 发现设备不匹配:")
            for mismatch in device_mismatches[:5]:  # 只显示前5个
                print(f"   {mismatch}")
            if len(device_mismatches) > 5:
                print(f"   ... 还有 {len(device_mismatches) - 5} 个不匹配")

            # 尝试强制移动所有组件到目标设备
            print(f"🔧 强制移动所有模型组件到 {target_device}")
            model = model.to(target_device)

            # 递归处理子模块
            for module in model.modules():
                module = module.to(target_device)
        else:
            if not self.is_distributed or self.local_rank == 0:
                print(f"✅ 所有模型参数都在 {target_device} 上")

    def _verify_training_device_consistency(self, labels):
        """验证训练时的设备一致性"""
        if not self.is_distributed or self.local_rank == 0:
            print(f"🔍 验证训练设备一致性...")

            # 安全检查模型设备
            try:
                model_device = next(self.model.parameters()).device
            except StopIteration:
                # 如果模型没有参数，使用目标设备
                model_device = self.device
            print(f"   模型设备: {model_device}")
            print(f"   标签设备: {labels.device}")
            print(f"   目标设备: {self.device}")

            # 检查设备一致性
            if model_device != self.device:
                print(f"⚠️ 模型设备不匹配: {model_device} != {self.device}")
            if labels.device != self.device:
                print(f"⚠️ 标签设备不匹配: {labels.device} != {self.device}")

            if model_device == self.device and labels.device == self.device:
                print(f"✅ 设备一致性检查通过")

    def _ensure_input_device_consistency(self, patient_data, labels):
        """确保输入数据的设备一致性"""
        if not self.is_distributed or self.local_rank == 0:
            print(f"🔍 检查输入数据设备一致性...")
            print(f"   标签设备: {labels.device}")
            print(f"   目标设备: {self.device}")

            # 检查patient_data中的数值数据（如果有的话）
            if isinstance(patient_data, list) and len(patient_data) > 0:
                sample_data = patient_data[0]
                if isinstance(sample_data, dict):
                    for key, value in sample_data.items():
                        if isinstance(value, torch.Tensor):
                            print(f"   {key} 设备: {value.device}")
                            if value.device != self.device:
                                print(f"⚠️ 发现设备不匹配: {key} 在 {value.device}，应该在 {self.device}")

    def convert_batch_to_patient_data(self, batch):
        """将batch转换为patient_data格式 - 优化版本"""
        texts = batch['text']
        numeric = batch['numeric'].to(self.device, non_blocking=True)
        labels = batch['label'].to(self.device, non_blocking=True)

        # 批量处理文本解析，避免逐个处理
        batch_size = len(texts)
        patient_data = []

        # 预分配列表避免动态扩展
        patient_data = [{}] * batch_size

        # 向量化处理数值特征
        numeric_cpu = numeric.cpu()

        for i in range(batch_size):
            patient_dict = {}
            text = texts[i]

            # 优化的文本解析 - 减少字符串操作
            if "年龄" in text:
                try:
                    # 使用更高效的字符串处理
                    age_start = text.find("年龄") + 2
                    age_end = text.find("岁", age_start)
                    if age_end > age_start:
                        age_str = text[age_start:age_end].strip(": ")
                        patient_dict['年龄num'] = float(age_str)
                    else:
                        patient_dict['年龄num'] = 0.0
                except:
                    patient_dict['年龄num'] = 0.0

            if "症状:" in text:
                try:
                    symptom_start = text.find("症状:") + 3
                    symptom_end = text.find(" ", symptom_start)
                    if symptom_end > symptom_start:
                        patient_dict['症状'] = text[symptom_start:symptom_end]
                    else:
                        patient_dict['症状'] = text[symptom_start:symptom_start+10]  # 取前10个字符
                except:
                    patient_dict['症状'] = '无症状'

            # 批量添加数值特征 - 避免重复的tensor操作
            numeric_values = numeric_cpu[i]
            for j, feature_name in enumerate(NUMERIC_FEATURES):
                if j < len(numeric_values):
                    patient_dict[feature_name] = numeric_values[j].item()

            patient_data[i] = patient_dict

        return patient_data, labels

    def train_epoch(self, train_loader, epoch):
        """训练一个epoch - 优化版本"""
        self.model.train()
        total_loss = 0

        # 🔥 修复：训练前清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()

        # 开始epoch性能监控
        self.performance_monitor.start_epoch()

        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}")

        for step, batch in enumerate(pbar):
            batch_start_time = time.time()
            # 转换数据格式
            patient_data, labels = self.convert_batch_to_patient_data(batch)

            # 在第一个batch时验证设备一致性
            if step == 0 and epoch == 0:
                self._verify_training_device_consistency(labels)

            # 前向传播
            if USE_MIXED_PRECISION:
                with autocast():
                    # 确保输入数据在正确设备上
                    if step == 0 and epoch == 0:
                        self._ensure_input_device_consistency(patient_data, labels)

                    probs, logits = self.model(patient_data)
                    
                    # 在DataParallel模式下，确保logits和labels的批次大小匹配
                    if self.is_distributed == "dataparallel":
                        # DataParallel会自动收集所有GPU的输出，但labels没有被复制
                        if logits.size(0) != labels.size(0):
                            expected_batch_size = logits.size(0)
                            current_batch_size = labels.size(0)
                            # 重复labels以匹配logits的批次大小
                            labels = labels.repeat(expected_batch_size // current_batch_size, 1)
                    
                    loss = self.criterion(logits, labels.float())

                # 反向传播
                self.scaler.scale(loss).backward()
                
                # 梯度裁剪
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                self.scaler.step(self.optimizer)
                self.scaler.update()
                self.optimizer.zero_grad()

                # 更新学习率
                if hasattr(self, 'scheduler'):
                    self.scheduler.step()
            else:
                # 确保输入数据在正确设备上
                if step == 0 and epoch == 0:
                    self._ensure_input_device_consistency(patient_data, labels)

                probs, logits = self.model(patient_data)
                
                # 在DataParallel模式下，确保logits和labels的批次大小匹配
                if self.is_distributed == "dataparallel":
                    # DataParallel会自动收集所有GPU的输出，但labels没有被复制
                    if logits.size(0) != labels.size(0):
                        expected_batch_size = logits.size(0)
                        current_batch_size = labels.size(0)
                        # 重复labels以匹配logits的批次大小
                        labels = labels.repeat(expected_batch_size // current_batch_size, 1)
                
                loss = self.criterion(logits, labels.float())

                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()
                self.optimizer.zero_grad()

                # 更新学习率
                if hasattr(self, 'scheduler'):
                    self.scheduler.step()

            total_loss += loss.item()

            # 记录batch性能
            self.performance_monitor.record_batch(batch_start_time)

            # 更新进度条
            pbar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'lr': f'{self.optimizer.param_groups[0]["lr"]:.2e}'
            })

            # 🔥 修复：定期清理GPU内存
            if (step + 1) % 100 == 0 and torch.cuda.is_available():
                torch.cuda.empty_cache()

        # 打印epoch性能统计
        if not self.is_distributed or self.local_rank == 0:
            self.performance_monitor.print_stats(epoch)

        return total_loss / len(train_loader)


    def validate_epoch(self, val_loader):
        """验证epoch - 支持多疾病详细评估，参考multi_gpu_train.py"""
        self.model.eval()
        total_loss = 0
        all_preds = []
        all_labels = []
        all_probs = []

        with torch.no_grad():
            if not self.is_distributed or self.local_rank == 0:
                pbar = tqdm(val_loader, desc="Validating")
            else:
                pbar = val_loader

            for batch in pbar:
                # 转换数据格式
                patient_data, labels = self.convert_batch_to_patient_data(batch)

                # 前向传播
                if USE_MIXED_PRECISION:
                    with autocast():
                        probs, logits = self.model(patient_data)
                        
                        # 在DataParallel模式下，确保logits和labels的批次大小匹配
                        if self.is_distributed == "dataparallel":
                            if logits.size(0) != labels.size(0):
                                expected_batch_size = logits.size(0)
                                current_batch_size = labels.size(0)
                                labels = labels.repeat(expected_batch_size // current_batch_size, 1)
                        
                        loss = self.criterion(logits, labels.float())
                else:
                    probs, logits = self.model(patient_data)
                    
                    # 在DataParallel模式下，确保logits和labels的批次大小匹配
                    if self.is_distributed == "dataparallel":
                        if logits.size(0) != labels.size(0):
                            expected_batch_size = logits.size(0)
                            current_batch_size = labels.size(0)
                            labels = labels.repeat(expected_batch_size // current_batch_size, 1)
                    
                    loss = self.criterion(logits, labels.float())

                total_loss += loss.item()
                all_probs.append(probs.cpu())
                all_labels.append(labels.cpu())

        # 合并结果
        all_probs = torch.cat(all_probs, dim=0)
        all_labels = torch.cat(all_labels, dim=0)

        # 计算最优阈值（支持多疾病）
        if DYNAMIC_THRESHOLD:
            optimal_threshold = self.find_optimal_threshold(all_probs, all_labels)
            if not self.is_distributed or self.local_rank == 0:
                if isinstance(optimal_threshold, list):
                    print(f"🎯 使用多疾病阈值（显示前3个）: {optimal_threshold[:3]} ...")
                else:
                    print(f"🎯 最优阈值: {optimal_threshold:.3f} (默认: {PREDICTION_THRESHOLD})")
        else:
            optimal_threshold = PREDICTION_THRESHOLD if len(TARGET_DISEASES) == 1 else [PREDICTION_THRESHOLD]*len(TARGET_DISEASES)
            if not self.is_distributed or self.local_rank == 0:
                print(f"🎯 使用固定阈值: {optimal_threshold}")

        # 根据阈值计算预测结果（向量化支持）
        if isinstance(optimal_threshold, list):
            th = torch.tensor(optimal_threshold, dtype=all_probs.dtype, device=all_probs.device)
            all_preds = (all_probs > th).float()
        else:
            all_preds = (all_probs > optimal_threshold).float()

        # 计算指标
        avg_loss = total_loss / len(val_loader)

        if len(TARGET_DISEASES) == 1:
            # 单疾病指标计算
            preds_np = all_preds.numpy().flatten()
            labels_np = all_labels.numpy().flatten()
            probs_np = all_probs.numpy().flatten()

            acc = accuracy_score(labels_np, preds_np)
            precision = precision_score(labels_np, preds_np, zero_division=0)
            recall = recall_score(labels_np, preds_np, zero_division=0)
            f1 = f1_score(labels_np, preds_np, zero_division=0)

            try:
                auc = roc_auc_score(labels_np, probs_np)
            except:
                auc = 0.0

            pred_pos = int(preds_np.sum())
            pred_neg = len(preds_np) - pred_pos
            true_pos = int(labels_np.sum())
            true_neg = len(labels_np) - true_pos

            # 计算混淆矩阵
            tp = int(((preds_np == 1) & (labels_np == 1)).sum())
            tn = int(((preds_np == 0) & (labels_np == 0)).sum())
            fp = int(((preds_np == 1) & (labels_np == 0)).sum())
            fn = int(((preds_np == 0) & (labels_np == 1)).sum())

        else:
            # 多疾病指标计算 - 使用第一个疾病作为代表，但同时计算所有疾病的详细指标
            preds_np = all_preds[:, 0].numpy()
            labels_np = all_labels[:, 0].numpy()
            probs_np = all_probs[:, 0].numpy()

            acc = accuracy_score(labels_np, preds_np)
            precision = precision_score(labels_np, preds_np, zero_division=0)
            recall = recall_score(labels_np, preds_np, zero_division=0)
            f1 = f1_score(labels_np, preds_np, zero_division=0)

            try:
                auc = roc_auc_score(labels_np, probs_np)
            except:
                auc = 0.0

            pred_pos = int(preds_np.sum())
            pred_neg = len(preds_np) - pred_pos
            true_pos = int(labels_np.sum())
            true_neg = len(labels_np) - true_pos

            # 计算混淆矩阵
            tp = int(((preds_np == 1) & (labels_np == 1)).sum())
            tn = int(((preds_np == 0) & (labels_np == 0)).sum())
            fp = int(((preds_np == 1) & (labels_np == 0)).sum())
            fn = int(((preds_np == 0) & (labels_np == 1)).sum())

            # 🔥 新增：计算每个疾病的详细指标（参考multi_gpu_train.py）
            disease_metrics = {}
            if not self.is_distributed or self.local_rank == 0:
                print(f"📊 各疾病详细指标:")
                for i, disease in enumerate(TARGET_DISEASES):
                    disease_preds = all_preds[:, i].numpy()
                    disease_labels = all_labels[:, i].numpy()
                    disease_f1 = f1_score(disease_labels, disease_preds, zero_division=0)
                    disease_precision = precision_score(disease_labels, disease_preds, zero_division=0)
                    disease_recall = recall_score(disease_labels, disease_preds, zero_division=0)
                    disease_acc = accuracy_score(disease_labels, disease_preds)
                    
                    disease_pos = int(disease_preds.sum())
                    disease_true_pos = int(disease_labels.sum())
                    
                    # 计算每个疾病的混淆矩阵
                    disease_tp = int(((disease_preds == 1) & (disease_labels == 1)).sum())
                    disease_tn = int(((disease_preds == 0) & (disease_labels == 0)).sum())
                    disease_fp = int(((disease_preds == 1) & (disease_labels == 0)).sum())
                    disease_fn = int(((disease_preds == 0) & (disease_labels == 1)).sum())
                    
                    # 计算医疗指标
                    disease_sensitivity = disease_tp / (disease_tp + disease_fn) if (disease_tp + disease_fn) > 0 else 0.0
                    disease_specificity = disease_tn / (disease_tn + disease_fp) if (disease_tn + disease_fp) > 0 else 0.0
                    
                    disease_metrics[disease] = {
                        'accuracy': disease_acc,
                        'precision': disease_precision,
                        'recall': disease_recall,
                        'f1': disease_f1,
                        'sensitivity': disease_sensitivity,
                        'specificity': disease_specificity,
                        'pred_positive': disease_pos,
                        'true_positive': disease_true_pos,
                        'tp': disease_tp,
                        'tn': disease_tn,
                        'fp': disease_fp,
                        'fn': disease_fn
                    }
                    
                    print(f"   {disease}: F1={disease_f1:.3f}, 精确率={disease_precision:.3f}, 召回率={disease_recall:.3f}, 敏感性={disease_sensitivity:.3f}, 特异性={disease_specificity:.3f}")
                    print(f"     预测阳性={disease_pos}, 真实阳性={disease_true_pos}, TP={disease_tp}, TN={disease_tn}, FP={disease_fp}, FN={disease_fn}")

        # 计算医疗评价指标
        sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0.0  # 敏感性(召回率)
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0.0  # 特异性
        ppv = tp / (tp + fp) if (tp + fp) > 0 else 0.0  # 阳性预测值(精确率)
        npv = tn / (tn + fn) if (tn + fn) > 0 else 0.0  # 阴性预测值
        
        result = {
            'val_loss': avg_loss,
            'accuracy': acc,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'auc': auc,
            'pred_positive': pred_pos,
            'pred_negative': pred_neg,
            'true_positive': true_pos,
            'true_negative': true_neg,
            # 医疗评价指标
            'sensitivity': sensitivity,  # 敏感性(召回率)
            'specificity': specificity,  # 特异性
            'ppv': ppv,  # 阳性预测值(精确率)
            'npv': npv,  # 阴性预测值
            'tp': tp,  # 真阳性
            'tn': tn,  # 真阴性
            'fp': fp,  # 假阳性
            'fn': fn,   # 假阴性
            'optimal_threshold': (optimal_threshold if not isinstance(optimal_threshold, list) else (optimal_threshold[0] if optimal_threshold else PREDICTION_THRESHOLD))  # 兼容旧字段
        }
        # 多疾病时保存每个疾病的阈值
        if isinstance(optimal_threshold, list):
            result['optimal_thresholds'] = {d: float(optimal_threshold[i]) for i, d in enumerate(TARGET_DISEASES)}
        
        # 🔥 新增：如果是多疾病，添加每个疾病的详细指标
        if len(TARGET_DISEASES) > 1:
            result['disease_metrics'] = disease_metrics
        
        return result
    
    def train(self, train_dataset, val_dataset):
        """训练模型 - 支持多GPU"""
        if not self.is_distributed or self.local_rank == 0:
            print(f"🚀 开始训练...")

        # 分析数据分布（只在主进程）
        if not self.is_distributed or self.local_rank == 0:
            pos_ratio, train_labels = self.analyze_data_distribution(train_dataset)
        
        # 创建采样器
        train_sampler = None
        val_sampler = None
        shuffle = True
        
        if self.is_distributed and self.is_distributed != "dataparallel":
            # DDP模式：使用DistributedSampler
            train_sampler = DistributedSampler(
                train_dataset, 
                num_replicas=self.world_size, 
                rank=self.local_rank,
                shuffle=True
            )
            val_sampler = DistributedSampler(
                val_dataset, 
                num_replicas=self.world_size, 
                rank=self.local_rank,
                shuffle=False
            )
            shuffle = False
            if self.local_rank == 0:
                print(f"🔗 使用DistributedSampler进行数据分布")
        else:
            # DataParallel或单GPU模式：使用普通采样器
            if not self.is_distributed or self.local_rank == 0:
                pos_ratio, train_labels = self.analyze_data_distribution(train_dataset)
                # 多标签优先使用多标签采样器
                if ENABLE_MULTILABEL_SAMPLER and isinstance(train_labels, np.ndarray) and train_labels.ndim == 2 and len(TARGET_DISEASES) > 1:
                    balanced_sampler = self.create_multilabel_balanced_sampler(train_labels)
                else:
                    balanced_sampler = self.create_balanced_sampler(train_labels)
                if balanced_sampler is not None:
                    train_sampler = balanced_sampler
                    shuffle = False

        # 创建数据加载器 - 内存优化版本
        # 根据分布式模式调整参数，优先节省内存
        use_pin_memory = False  # 🔥 修复：禁用pin_memory节省内存
        num_workers = DATALOADER_NUM_WORKERS if not self.is_distributed else max(1, DATALOADER_NUM_WORKERS // 4)  # 🔥 修复：进一步减少worker

        train_loader = DataLoader(
            train_dataset,
            batch_size=BATCH_SIZE,
            shuffle=shuffle,
            sampler=train_sampler,
            num_workers=num_workers,
            pin_memory=use_pin_memory,
            pin_memory_device=PIN_MEMORY_DEVICE if use_pin_memory else "",
            prefetch_factor=PREFETCH_FACTOR if num_workers > 0 else None,
            persistent_workers=False,  # 🔥 修复：禁用persistent_workers节省内存
            drop_last=True  # 丢弃最后一个不完整的batch，保证训练稳定性
        )
        val_loader = DataLoader(
            val_dataset,
            batch_size=BATCH_SIZE,
            shuffle=False,
            sampler=val_sampler,
            num_workers=num_workers,
            pin_memory=use_pin_memory,
            pin_memory_device=PIN_MEMORY_DEVICE if use_pin_memory else "",
            prefetch_factor=PREFETCH_FACTOR if num_workers > 0 else None,
            persistent_workers=False,  # 🔥 修复：禁用persistent_workers节省内存
            drop_last=False
        )

        if not self.is_distributed or self.local_rank == 0:
            print(f"📊 数据加载器配置:")
            print(f"   Workers: {num_workers}, Pin Memory: {use_pin_memory}")
            print(f"   Prefetch Factor: {PREFETCH_FACTOR}, Persistent Workers: {num_workers > 0}")

        # 创建模型
        self.create_model()

        # 初始化学习率调度器
        total_steps = EPOCHS * (len(train_dataset) // BATCH_SIZE)
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=total_steps,
            eta_min=LEARNING_RATE * 0.1
        )

        if not self.is_distributed or self.local_rank == 0:
            print(f"📈 学习率调度: 余弦退火 (总步数: {total_steps})")

        best_f1 = 0.0
        best_auc = 0.0
        patience_counter = 0
        
        # 初始化训练历史记录
        if not self.is_distributed or self.local_rank == 0:
            self.training_history['train_losses'] = []
            self.training_history['val_losses'] = []
            self.training_history['val_metrics'] = []

        for epoch in range(EPOCHS):
            if not self.is_distributed or self.local_rank == 0:
                print(f"\n=== Epoch {epoch+1}/{EPOCHS} ===")
            
            # 设置epoch（用于DistributedSampler）
            if self.is_distributed and self.is_distributed != "dataparallel" and hasattr(train_sampler, 'set_epoch'):
                train_sampler.set_epoch(epoch)

            # 训练
            train_loss = self.train_epoch(train_loader, epoch)

            # 验证
            val_metrics = self.validate_epoch(val_loader)

            # 只在主进程打印结果和记录历史
            if not self.is_distributed or self.local_rank == 0:
                # 记录训练历史
                self.training_history['train_losses'].append(train_loss)
                self.training_history['val_losses'].append(val_metrics['val_loss'])
                self.training_history['val_metrics'].append({
                    'epoch': epoch + 1,
                    'train_loss': train_loss,
                    **val_metrics
                })
                
                print(f"📊 训练损失: {train_loss:.4f}")
                print(f"📊 验证损失: {val_metrics['val_loss']:.4f}")
                print(f"📊 准确率: {val_metrics['accuracy']:.4f}")
                print(f"📊 精确率: {val_metrics['precision']:.4f}")
                print(f"📊 召回率: {val_metrics['recall']:.4f}")
                print(f"📊 F1分数: {val_metrics['f1']:.4f}")
                print(f"📊 AUC: {val_metrics['auc']:.4f}")
                print(f"📊 敏感性: {val_metrics['sensitivity']:.4f}")
                print(f"📊 特异性: {val_metrics['specificity']:.4f}")
                print(f"📊 阳性预测值: {val_metrics['ppv']:.4f}")
                print(f"📊 阴性预测值: {val_metrics['npv']:.4f}")

                # 预测分布
                print(f"🎯 预测分布:")
                print(f"   预测阳性: {val_metrics['pred_positive']}/{val_metrics['pred_positive'] + val_metrics['pred_negative']} ({val_metrics['pred_positive']/(val_metrics['pred_positive'] + val_metrics['pred_negative'])*100:.2f}%)")
                print(f"   真实阳性: {val_metrics['true_positive']}/{val_metrics['true_positive'] + val_metrics['true_negative']} ({val_metrics['true_positive']/(val_metrics['true_positive'] + val_metrics['true_negative'])*100:.2f}%)")

            # 保存最佳模型（使用医疗优化评分）
            current_f1 = val_metrics['f1']
            current_auc = val_metrics['auc']

            # 计算医疗评分
            if hasattr(self, 'disease_prevalence') and len(TARGET_DISEASES) > 0:
                # 使用第一个疾病的患病率作为代表
                representative_disease = TARGET_DISEASES[0]
                prevalence = self.disease_prevalence.get(representative_disease, 0.05)
            else:
                prevalence = 0.05  # 默认患病率

            # 获取代表性疾病名称
            representative_disease = TARGET_DISEASES[0] if TARGET_DISEASES else None

            current_medical_score = self.calculate_medical_score(
                val_metrics['precision'], val_metrics['recall'],
                val_metrics['specificity'], prevalence, representative_disease
            )

            # 根据配置选择主要优化指标
            if MEDICAL_OPTIMIZATION['primary_metric'] == 'medical_score':
                current_primary = current_medical_score
                best_primary = getattr(self, 'best_medical_score', 0)
            elif MEDICAL_OPTIMIZATION['primary_metric'] == 'auc':
                current_primary = current_auc
                best_primary = best_auc
            else:  # 默认使用F1
                current_primary = current_f1
                best_primary = best_f1

            # 判断是否改善
            improved = False
            if current_primary > best_primary:
                improved = True
            elif current_primary == best_primary and current_auc > best_auc:
                improved = True

            # 医疗安全检查：召回率不能过低
            if val_metrics['recall'] < MEDICAL_OPTIMIZATION['min_recall_threshold']:
                improved = False
                if not self.is_distributed or self.local_rank == 0:
                    print(f"⚠️ 召回率过低 ({val_metrics['recall']:.3f} < {MEDICAL_OPTIMIZATION['min_recall_threshold']})，不保存模型")

            if improved:
                best_f1 = current_f1
                best_auc = current_auc
                self.best_medical_score = current_medical_score
                patience_counter = 0

                # 只在主进程保存模型
                if not self.is_distributed or self.local_rank == 0:
                    # 获取要保存的模型状态
                    if self.is_distributed == "dataparallel":
                        model_state_dict = self.model.module.state_dict()
                    elif self.is_distributed:
                        model_state_dict = self.model.module.state_dict()
                    else:
                        model_state_dict = self.model.state_dict()
                    
                    torch.save({
                        'model_state_dict': model_state_dict,
                        'optimizer_state_dict': self.optimizer.state_dict(),
                        'epoch': epoch + 1,
                        'f1': best_f1,
                        'auc': best_auc,
                        'strategy': IMBALANCE_STRATEGY,
                        'threshold': PREDICTION_THRESHOLD,
                        'config': {
                            'target_diseases': TARGET_DISEASES,
                            'qwen_model': QWEN_MODEL_NAME
                        }
                    }, SAVE_PATH)
                    print(f"🎉 新的最佳模型! F1: {best_f1:.4f}, AUC: {best_auc:.4f}")
            else:
                patience_counter += 1
                print(f"⏳ 未改善 ({patience_counter}/{EARLY_STOPPING_PATIENCE})")

            # 早停
            if patience_counter >= EARLY_STOPPING_PATIENCE:
                if not self.is_distributed or self.local_rank == 0:
                    print(f"🛑 早停! 连续{EARLY_STOPPING_PATIENCE}轮未改善")
                break

        if not self.is_distributed or self.local_rank == 0:
            print(f"\n✅ 训练完成!")
            print(f"📈 最佳F1: {best_f1:.4f}")
            print(f"📈 最佳AUC: {best_auc:.4f}")

            if best_f1 == 0:
                print(f"⚠️ F1分数仍为0，建议:")
                print(f"   1. 降低PREDICTION_THRESHOLD到0.1-0.2")
                print(f"   2. 增加FOCAL_ALPHA到0.05")
                print(f"   3. 增加POS_WEIGHT到500+")
            
            # 保存完整的训练结果
            self.save_training_results(best_f1, best_auc)

        return {'best_f1': best_f1, 'best_auc': best_auc}
    
    def save_training_results(self, best_f1, best_auc):
        """保存完整的训练结果到JSON文件"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = Path("results")
        results_dir.mkdir(exist_ok=True)
        
        results_file = results_dir / f"training_results_{timestamp}.json"
        
        # 获取最佳模型的详细评估结果
        best_epoch_idx = -1
        best_metrics = {}
        if self.training_history['val_metrics']:
            # 找到F1分数最高的epoch
            f1_scores = [m['f1'] for m in self.training_history['val_metrics']]
            best_epoch_idx = f1_scores.index(max(f1_scores))
            best_metrics = self.training_history['val_metrics'][best_epoch_idx]
        
        # 🔥 新增：获取每种疾病的详细指标（如果有的话）
        disease_metrics_summary = {}
        if best_metrics and 'disease_metrics' in best_metrics:
            disease_metrics_summary = best_metrics['disease_metrics']
            print(f"📊 最佳模型包含 {len(disease_metrics_summary)} 种疾病的详细指标")
        
        # 构建完整的训练结果
        training_results = {
            'training_info': {
                'timestamp': timestamp,
                'total_epochs': len(self.training_history['train_losses']),
                'early_stopped': len(self.training_history['train_losses']) < EPOCHS,
                'best_epoch': best_epoch_idx + 1 if best_epoch_idx >= 0 else None
            },
            'config': self.training_history['config'],
            'training_curves': {
                'train_losses': self.training_history['train_losses'],
                'val_losses': self.training_history['val_losses']
            },
            'epoch_metrics': self.training_history['val_metrics'],
            'best_model_performance': {
                'f1': best_f1,
                'auc': best_auc,
                'detailed_metrics': best_metrics
            },
            'medical_evaluation_summary': {
                'description': 'Medical AI model evaluation metrics',
                'metrics_explanation': {
                    'sensitivity': 'True Positive Rate - ability to identify disease cases',
                    'specificity': 'True Negative Rate - ability to identify healthy cases',
                    'ppv': 'Positive Predictive Value - precision of positive predictions',
                    'npv': 'Negative Predictive Value - precision of negative predictions',
                    'f1': 'Harmonic mean of precision and recall',
                    'auc': 'Area Under ROC Curve - overall discrimination ability'
                },
                'confusion_matrix': best_metrics.get('tp', 0) + best_metrics.get('tn', 0) + best_metrics.get('fp', 0) + best_metrics.get('fn', 0) if best_metrics else 0
            }
        }
        
        # 🔥 新增：如果有每种疾病的详细指标，则添加到结果中
        if disease_metrics_summary:
            training_results['disease_detailed_metrics'] = {
                'description': 'Detailed metrics for each target disease',
                'diseases': disease_metrics_summary,
                'summary_statistics': {
                    'avg_f1': sum(metrics['f1'] for metrics in disease_metrics_summary.values()) / len(disease_metrics_summary),
                    'avg_precision': sum(metrics['precision'] for metrics in disease_metrics_summary.values()) / len(disease_metrics_summary),
                    'avg_recall': sum(metrics['recall'] for metrics in disease_metrics_summary.values()) / len(disease_metrics_summary),
                    'avg_sensitivity': sum(metrics['sensitivity'] for metrics in disease_metrics_summary.values()) / len(disease_metrics_summary),
                    'avg_specificity': sum(metrics['specificity'] for metrics in disease_metrics_summary.values()) / len(disease_metrics_summary),
                    'best_f1_disease': max(disease_metrics_summary.items(), key=lambda x: x[1]['f1'])[0],
                    'worst_f1_disease': min(disease_metrics_summary.items(), key=lambda x: x[1]['f1'])[0],
                    'total_diseases': len(disease_metrics_summary)
                }
            }
            
            # 打印疾病汇总统计
            print(f"🎯 疾病指标汇总:")
            print(f"   平均F1分数: {training_results['disease_detailed_metrics']['summary_statistics']['avg_f1']:.3f}")
            print(f"   平均精确率: {training_results['disease_detailed_metrics']['summary_statistics']['avg_precision']:.3f}")
            print(f"   平均召回率: {training_results['disease_detailed_metrics']['summary_statistics']['avg_recall']:.3f}")
            print(f"   平均敏感性: {training_results['disease_detailed_metrics']['summary_statistics']['avg_sensitivity']:.3f}")
            print(f"   平均特异性: {training_results['disease_detailed_metrics']['summary_statistics']['avg_specificity']:.3f}")
            print(f"   最佳F1疾病: {training_results['disease_detailed_metrics']['summary_statistics']['best_f1_disease']}")
            print(f"   最差F1疾病: {training_results['disease_detailed_metrics']['summary_statistics']['worst_f1_disease']}")
        
        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(training_results, f, indent=2, ensure_ascii=False)
            print(f"💾 训练结果已保存: {results_file}")
            print(f"📊 包含 {len(self.training_history['val_metrics'])} 个epoch的完整记录")
        except Exception as e:
            print(f"⚠️ 保存训练结果失败: {e}")


def main():
    """主函数"""
    print(f"🎯 简化版Qwen医疗预测训练")
    print(f"=" * 50)
    
    # 显示缓存配置
    print(f"💾 缓存配置:")
    print(f"   启用缓存: {USE_CACHE}")
    print(f"   缓存目录: {CACHE_DIR}")
    
    if USE_CACHE:
        list_cache_files()
        print()

    # 加载数据（带缓存）
    print(f"📂 加载数据...")
    train_dataset, val_dataset, test_dataset, scaler, feature_info = load_data_with_cache(
        csv_path=DATA_PATH,
        target_diseases=TARGET_DISEASES,
        prediction_mode=PREDICTION_MODE,
        test_size=0.2,
        max_samples=MAX_SAMPLES,
        time_gap_years=1
    )

    if train_dataset is None:
        print(f"❌ 数据加载失败")
        return

    # 训练
    trainer = SimpleQwenTrainer()
    try:
        results = trainer.train(train_dataset, val_dataset)
        if not trainer.is_distributed or trainer.local_rank == 0:
            print(f"\n🎉 训练成功!")
            print(f"💾 模型保存: {SAVE_PATH}")
    except Exception as e:
        if not trainer.is_distributed or trainer.local_rank == 0:
            print(f"❌ 训练失败: {e}")
            import traceback
            traceback.print_exc()
    finally:
        # 清理分布式训练
        if trainer.is_distributed and trainer.is_distributed != "dataparallel":
            dist.destroy_process_group()


if __name__ == "__main__":
    import sys
    
    # 处理命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "clear_cache":
            print("🗑️ 清理数据缓存...")
            clear_data_cache()
            exit(0)
        elif command == "list_cache":
            print("📂 列出缓存文件...")
            list_cache_files()
            exit(0)
        elif command == "no_cache":
            print("🔄 禁用缓存模式...")
            globals()['USE_CACHE'] = False
        else:
            print(f"❓ 未知命令: {command}")
            print(f"   可用命令: clear_cache, list_cache, no_cache")
            exit(1)
    
    main()
