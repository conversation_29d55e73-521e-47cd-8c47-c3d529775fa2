"""
快速分析118个疾病字段的样本分布

目标：找出样本数足够的疾病，重新定义TARGET_DISEASES

运行: python quick_disease_analysis.py
"""

import pandas as pd
import numpy as np

# 完整的疾病字段列表
ALL_DISEASE_FIELDS = [
    '艾滋病', '白癜风', '白内障', '白血病', '败血症', '瓣膜病', '包虫病', '鼻炎', '扁桃体炎', '丙肝',
    '肠炎', '胆管炎', '胆结石', '胆囊炎', '蛋白尿', '低血糖', '低血压', '癫痫', '动脉粥样硬化', '肥胖',
    '肺癌', '肺结节', '肺气肿', '肺纤维化', '肺心病', '肺炎', '风湿性关节炎', '风心病', '妇科炎症', '肝癌',
    '肝瘤', '肝炎', '肝硬化', '高血糖', '高血压', '高血脂', '高原病', '宫颈癌', '宫颈炎', '骨折',
    '骨质疏松症', '骨质增生', '关节炎', '冠心病', '黄疸', '寄生虫病', '甲肝', '甲亢', '甲状腺炎', '结节',
    '结膜炎', '颈椎病', '阑尾炎', '类风湿', '淋巴结', '卵巢囊肿', '慢阻肺', '梅毒', '脑出血', '脑梗',
    '脑膜炎', '脑缺血', '脑瘫', '尿道炎', '尿毒症', '尿路感染', '牛皮癣', '膀胱炎', '皮炎', '偏瘫',
    '贫血', '气管炎', '前列腺炎', '龋齿', '缺齿', '乳腺癌', '乳腺炎', '疝气', '神经病', '肾积水',
    '肾结石', '肾囊肿', '肾尿盐结晶', '肾衰', '肾炎', '肾盂肾炎', '糖尿病', '痛风', '外阴炎', '胃癌',
    '胃溃疡', '胃炎', '先心病', '哮喘', '心包炎', '心梗', '心肌病', '心肌缺血', '心肌炎', '心绞痛',
    '心衰', '心脏病', '胸膜炎', '胸痛', '血栓', '牙结石', '牙周病', '咽炎', '腰椎病', '胰腺炎',
    '乙肝', '癔症', '支气管炎', '脂肪肝', '痔疮', '紫癜', '紫绀', '结核病'
]


def quick_analysis(csv_path, sample_size=300000):
    """快速分析疾病分布"""

    print("🔍 快速分析118个疾病字段...")

    # 读取数据（使用tab分隔符，基于你之前的输出）
    try:
        df = pd.read_csv(
            csv_path,
            nrows=sample_size,
            delimiter='\t',
            encoding='utf-8',
            on_bad_lines='skip',
            engine='python'
        )
        print(f"成功加载 {len(df)} 行数据")

    except Exception as e:
        print(f"读取失败: {e}")
        return

    # 分析每个疾病的患病情况
    disease_stats = []

    print(f"\n📊 分析疾病分布...")

    for disease in ALL_DISEASE_FIELDS:
        if disease in df.columns:
            # 转换为数值，计算患病人数（>0）
            values = pd.to_numeric(df[disease], errors='coerce').fillna(0)
            patients = (values > 0).sum()
            total = len(values)
            percentage = patients / total * 100

            disease_stats.append({
                'disease': disease,
                'patients': patients,
                'total': total,
                'percentage': percentage
            })
        else:
            print(f"⚠️ 字段不存在: {disease}")

    # 按患病人数排序
    disease_stats.sort(key=lambda x: x['patients'], reverse=True)

    # 打印结果
    print(f"\n🏆 疾病患病人数排行榜 (TOP 100):")
    print(f"{'排名':<4} {'疾病':<12} {'患病人数':<8} {'总样本':<8} {'患病率':<8}")
    print("-" * 50)

    top_diseases = []
    for i, stats in enumerate(disease_stats[:100]):
        disease = stats['disease']
        patients = stats['patients']
        total = stats['total']
        percentage = stats['percentage']

        print(f"{i + 1:<4} {disease:<12} {patients:<8} {total:<8} {percentage:<8.2f}%")

        # 收集有足够样本的疾病（≥300例）
        if patients >= 300:
            top_diseases.append(disease)

    # 推荐疾病列表
    print(f"\n🎯 推荐的疾病列表 (患病≥300例):")
    recommended_diseases = top_diseases[:30]  # 取前30个

    for i, disease in enumerate(recommended_diseases):
        stats = next(s for s in disease_stats if s['disease'] == disease)
        print(f"  {i + 1}. {disease}: {stats['patients']}例 ({stats['percentage']:.1f}%)")

    # 生成代码
    print(f"\n📝 建议更新 dataset.py 中的 TARGET_DISEASES:")
    print("TARGET_DISEASES = [")
    for disease in recommended_diseases:
        print(f"    '{disease}',")
    print("]")

    # 检查原来选择的疾病
    print(f"\n🔍 检查原来选择的疾病:")
    old_diseases = ['高血压', '糖尿病', '贫血', '脂肪肝', '高血脂', '肾炎', '肥胖', '肝炎', '冠心病', '胃炎']

    for disease in old_diseases:
        stats = next((s for s in disease_stats if s['disease'] == disease), None)
        if stats:
            patients = stats['patients']
            percentage = stats['percentage']
            status = "✅ 够用" if patients >= 200 else "❌ 样本不足"
            print(f"  {disease}: {patients}例 ({percentage:.1f}%) - {status}")
        else:
            print(f"  {disease}: 字段不存在")

    return recommended_diseases


def main():
    csv_path = "/root/work_speace/prediction/4-total_3years-uniqPerson_clean.csv"

    print("=" * 60)
    print("🏥 快速疾病分布分析")
    print("=" * 60)

    recommended = quick_analysis(csv_path)

    if recommended:
        print(f"\n✅ 分析完成!")
        print(f"建议使用这{len(recommended)}个疾病进行训练")
        print(f"样本数都≥300，足够进行机器学习")
    else:
        print(f"\n❌ 分析失败，请检查文件路径和格式")


if __name__ == "__main__":
    main()