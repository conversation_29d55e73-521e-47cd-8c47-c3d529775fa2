#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习模型对比实验
复用深度学习训练代码的数据加载，对比传统机器学习模型性能
"""

from sklearn.utils.class_weight import compute_class_weight
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, roc_auc_score,
    classification_report, confusion_matrix, roc_curve, precision_recall_curve
)
from sklearn.model_selection import GridSearchCV, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.neural_network import MLPClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier
from sklearn.linear_model import LogisticRegression, RidgeClassifier
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, AdaBoostClassifier
import joblib
from tqdm import tqdm
import seaborn as sns
import matplotlib.pyplot as plt
import json
from datetime import datetime
import os
import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')


# 机器学习相关

# 数据处理和评估

# XGBoost (如果安装了)
try:
    import xgboost as xgb
    HAS_XGB = True
except ImportError:
    HAS_XGB = False
    print("⚠️ XGBoost未安装，跳过XGBoost模型")

# LightGBM (如果安装了)
try:
    import lightgbm as lgb
    HAS_LGB = True
except ImportError:
    HAS_LGB = False
    print("⚠️ LightGBM未安装，跳过LightGBM模型")

# 复用原有的数据加载
try:
    from dataset import load_unified_timeseries_data
    print("✅ 成功导入数据加载模块")
except ImportError as e:
    print(f"❌ 无法导入数据加载模块: {e}")
    print("请确保 dataset.py 文件在当前目录")
    exit(1)

# ==================== 配置区域 ====================

# 🎯 疾病预测配置 (复用深度学习配置)
TARGET_DISEASES = ['高血压', '脂肪肝', '糖尿病',
                   '高血脂', "支气管炎", "气管炎", "贫血", "肾囊肿", '冠心病']

# 📂 数据配置
DATA_PATH = "/root/work_speace/prediction/coronary_timeseries_data_fixed.csv"
MAX_SAMPLES = None  # 设为None使用全部数据，或设置数字限制样本数
PREDICTION_MODE = 'binary'

# 📊 实验配置
TEST_SIZE = 0.2           # 测试集比例
RANDOM_STATE = 42         # 随机种子
CV_FOLDS = 5              # 交叉验证折数
N_JOBS = -1               # 并行作业数 (-1表示使用所有CPU)

# 💾 结果保存配置
OUTPUT_DIR = "./ml_comparison_results"
SAVE_MODELS = True        # 是否保存训练好的模型
SAVE_PLOTS = True         # 是否保存可视化图表

# ==================== 数据处理类 ====================


class MLDataProcessor:
    """机器学习数据处理器"""

    def __init__(self, target_diseases):
        self.target_diseases = target_diseases
        self.scalers = {}
        self.label_encoders = {}
        self.feature_names = []

    def extract_features_from_dataset(self, dataset):
        """从PyTorch数据集中提取特征和标签"""
        print("📊 从数据集中提取特征...")

        features = []
        labels = []

        for i in tqdm(range(len(dataset)), desc="提取数据"):
            sample = dataset[i]

            # 处理文本特征 - 简单的文本编码
            text = sample['text']
            text_features = self._encode_text_features(text)

            # 处理数值特征
            numeric_features = sample['numeric'].numpy()

            # 处理标签
            label = sample['label']
            if label.dim() == 0:
                labels.append(label.item())
            else:
                labels.append(label.numpy())

            # 合并特征
            combined_features = np.concatenate(
                [text_features, numeric_features])
            features.append(combined_features)

        features = np.array(features)
        labels = np.array(labels)

        print(f"✅ 特征提取完成: {features.shape}, 标签: {labels.shape}")
        return features, labels

    def _encode_text_features(self, text):
        """简单的文本特征编码"""
        features = []

        # 年龄提取
        if "年龄" in text:
            try:
                age = float(text.split("年龄")[1].split("岁")[0].strip(": "))
                features.append(age)
            except:
                features.append(0)
        else:
            features.append(0)

        # 性别编码
        if "男" in text:
            features.extend([1, 0])
        elif "女" in text:
            features.extend([0, 1])
        else:
            features.extend([0, 0])

        # 症状关键词
        symptom_keywords = ['头痛', '胸痛', '心悸', '头晕', '乏力', '气短', '腰痛']
        for keyword in symptom_keywords:
            features.append(1 if keyword in text else 0)

        # 疾病史关键词
        history_keywords = ['高血压', '糖尿病', '心脏病', '肝病', '肾病']
        for keyword in history_keywords:
            features.append(1 if keyword in text else 0)

        # 生活习惯关键词
        habit_keywords = ['吸烟', '饮酒', '锻炼']
        for keyword in habit_keywords:
            features.append(1 if keyword in text else 0)

        return np.array(features)

    def prepare_features(self, train_dataset, val_dataset, test_dataset):
        """准备机器学习特征"""
        print("🔧 准备机器学习特征...")

        # 提取特征
        X_train, y_train = self.extract_features_from_dataset(train_dataset)
        X_val, y_val = self.extract_features_from_dataset(val_dataset)
        X_test, y_test = self.extract_features_from_dataset(test_dataset)

        # 合并训练和验证集
        X_train_full = np.vstack([X_train, X_val])
        y_train_full = np.concatenate([y_train, y_val])

        # 标准化特征
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train_full)
        X_test_scaled = self.scaler.transform(X_test)

        # 生成特征名称
        self.feature_names = self._generate_feature_names()

        print(f"✅ 特征准备完成:")
        print(f"   训练特征: {X_train_scaled.shape}")
        print(f"   测试特征: {X_test_scaled.shape}")
        print(f"   特征数量: {len(self.feature_names)}")

        return X_train_scaled, X_test_scaled, y_train_full, y_test

    def _generate_feature_names(self):
        """生成特征名称"""
        names = ['年龄', '性别_男', '性别_女']

        # 症状特征
        symptom_keywords = ['头痛', '胸痛', '心悸', '头晕', '乏力', '气短', '腰痛']
        names.extend([f'症状_{kw}' for kw in symptom_keywords])

        # 疾病史特征
        history_keywords = ['高血压', '糖尿病', '心脏病', '肝病', '肾病']
        names.extend([f'病史_{kw}' for kw in history_keywords])

        # 生活习惯特征
        habit_keywords = ['吸烟', '饮酒', '锻炼']
        names.extend([f'习惯_{kw}' for kw in habit_keywords])

        # 数值特征 (从原代码复制)
        numeric_features = [
            '体温', '脉率', '呼吸频率', '左侧收缩压', '左侧舒张压',
            '右侧收缩压', '右侧舒张压', '身高', '体重', '腰围', '体质指数',
            '血红蛋白', '白细胞', '血小板', '空腹血糖MMOL',
            '总胆固醇', '甘油三酯', '血清肌酐', '血尿素'
        ]
        names.extend(numeric_features)

        return names

# ==================== 模型定义 ====================


class MLModelCollection:
    """机器学习模型集合"""

    def __init__(self, random_state=42, n_jobs=-1):
        self.random_state = random_state
        self.n_jobs = n_jobs
        self.models = {}
        self.best_params = {}
        self._init_models()

    def _init_models(self):
        """初始化所有模型"""
        print("🤖 初始化机器学习模型...")

        # 1. 逻辑回归
        self.models['Logistic Regression'] = {
            'model': LogisticRegression(random_state=self.random_state, max_iter=1000),
            'params': {
                'C': [0.001, 0.01, 0.1, 1, 10, 100],
                'penalty': ['l1', 'l2'],
                'solver': ['liblinear']
            }
        }

        # 2. 决策树
        self.models['Decision Tree'] = {
            'model': DecisionTreeClassifier(random_state=self.random_state),
            'params': {
                'max_depth': [3, 5, 7, 10, None],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4]
            }
        }

        # 3. 随机森林
        self.models['Random Forest'] = {
            'model': RandomForestClassifier(random_state=self.random_state, n_jobs=self.n_jobs),
            'params': {
                'n_estimators': [50, 100, 200],
                'max_depth': [5, 10, None],
                'min_samples_split': [2, 5],
                'min_samples_leaf': [1, 2]
            }
        }

        # 4. 梯度提升
        self.models['Gradient Boosting'] = {
            'model': GradientBoostingClassifier(random_state=self.random_state),
            'params': {
                'n_estimators': [50, 100, 200],
                'learning_rate': [0.01, 0.1, 0.2],
                'max_depth': [3, 5, 7]
            }
        }

        # 5. SVM
        self.models['SVM'] = {
            'model': SVC(random_state=self.random_state, probability=True),
            'params': {
                'C': [0.1, 1, 10],
                'gamma': ['scale', 'auto', 0.001, 0.01],
                'kernel': ['rbf', 'linear']
            }
        }

        # 6. K近邻
        self.models['KNN'] = {
            'model': KNeighborsClassifier(n_jobs=self.n_jobs),
            'params': {
                'n_neighbors': [3, 5, 7, 9, 11],
                'weights': ['uniform', 'distance'],
                'metric': ['euclidean', 'manhattan']
            }
        }

        # 7. 朴素贝叶斯
        self.models['Naive Bayes'] = {
            'model': GaussianNB(),
            'params': {
                'var_smoothing': [1e-9, 1e-8, 1e-7, 1e-6]
            }
        }

        # 8. 多层感知机
        self.models['MLP'] = {
            'model': MLPClassifier(random_state=self.random_state, max_iter=500),
            'params': {
                'hidden_layer_sizes': [(50,), (100,), (50, 50), (100, 50)],
                'activation': ['relu', 'tanh'],
                'alpha': [0.0001, 0.001, 0.01],
                'learning_rate': ['constant', 'adaptive']
            }
        }

        # 9. XGBoost (如果可用)
        if HAS_XGB:
            self.models['XGBoost'] = {
                'model': xgb.XGBClassifier(random_state=self.random_state, eval_metric='logloss'),
                'params': {
                    'n_estimators': [50, 100, 200],
                    'learning_rate': [0.01, 0.1, 0.2],
                    'max_depth': [3, 5, 7],
                    'subsample': [0.8, 0.9, 1.0]
                }
            }

        # 10. LightGBM (如果可用)
        if HAS_LGB:
            self.models['LightGBM'] = {
                'model': lgb.LGBMClassifier(random_state=self.random_state, verbose=-1),
                'params': {
                    'n_estimators': [50, 100, 200],
                    'learning_rate': [0.01, 0.1, 0.2],
                    'max_depth': [3, 5, 7],
                    'num_leaves': [15, 31, 63]
                }
            }

        print(f"✅ 初始化了 {len(self.models)} 个模型")

# ==================== 实验运行器 ====================


class MLExperimentRunner:
    """机器学习实验运行器"""

    def __init__(self, target_diseases, output_dir):
        self.target_diseases = target_diseases
        self.output_dir = output_dir
        self.results = {}
        self.trained_models = {}

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 初始化组件
        self.data_processor = MLDataProcessor(target_diseases)
        self.model_collection = MLModelCollection()

    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("📂 加载数据...")

        # 复用深度学习的数据加载
        train_dataset, val_dataset, test_dataset, scaler, feature_info = load_unified_timeseries_data(
            csv_path=DATA_PATH,
            target_diseases=self.target_diseases,
            prediction_mode=PREDICTION_MODE,
            test_size=TEST_SIZE,
            max_samples=MAX_SAMPLES,
            time_gap_years=1
        )

        if train_dataset is None:
            raise ValueError("数据加载失败")

        print(f"✅ 数据加载成功:")
        print(f"   训练集: {len(train_dataset)} 样本")
        print(f"   验证集: {len(val_dataset)} 样本")
        print(f"   测试集: {len(test_dataset)} 样本")

        # 准备机器学习特征
        X_train, X_test, y_train, y_test = self.data_processor.prepare_features(
            train_dataset, val_dataset, test_dataset
        )

        # 处理多疾病标签 - 使用第一个疾病
        if len(y_train.shape) > 1:
            print(f"🎯 多疾病设置，使用 {self.target_diseases[0]} 进行对比实验")
            y_train = y_train[:, 0]
            y_test = y_test[:, 0]

        # 分析数据分布
        self._analyze_data_distribution(y_train, y_test)

        return X_train, X_test, y_train, y_test

    def _analyze_data_distribution(self, y_train, y_test):
        """分析数据分布"""
        train_pos = (y_train == 1).sum()
        train_neg = (y_train == 0).sum()
        test_pos = (y_test == 1).sum()
        test_neg = (y_test == 0).sum()

        print(f"📊 数据分布分析:")
        print(
            f"   训练集: 阳性={train_pos}, 阴性={train_neg}, 比例={train_pos/(train_pos+train_neg)*100:.2f}%")
        print(
            f"   测试集: 阳性={test_pos}, 阴性={test_neg}, 比例={test_pos/(test_pos+test_neg)*100:.2f}%")

    def run_single_model(self, model_name, model_info, X_train, X_test, y_train, y_test):
        """运行单个模型实验"""
        print(f"\n🔬 实验: {model_name}")

        model = model_info['model']
        params = model_info['params']

        # 处理类别不均衡
        if hasattr(model, 'class_weight'):
            model.set_params(class_weight='balanced')

        # 网格搜索最佳参数
        print(f"   🔍 网格搜索最佳参数...")
        cv = StratifiedKFold(n_splits=CV_FOLDS, shuffle=True,
                             random_state=RANDOM_STATE)

        grid_search = GridSearchCV(
            model, params,
            cv=cv,
            scoring='f1',  # 使用F1分数作为优化目标
            n_jobs=N_JOBS,
            verbose=0
        )

        # 训练
        grid_search.fit(X_train, y_train)
        best_model = grid_search.best_estimator_

        print(f"   ✅ 最佳参数: {grid_search.best_params_}")
        print(f"   ✅ 交叉验证F1: {grid_search.best_score_:.4f}")

        # 预测
        y_pred = best_model.predict(X_test)
        y_pred_proba = best_model.predict_proba(X_test)[:, 1] if hasattr(
            best_model, 'predict_proba') else None

        # 计算指标
        metrics = self._calculate_metrics(y_test, y_pred, y_pred_proba)
        metrics['cv_f1'] = grid_search.best_score_
        metrics['best_params'] = grid_search.best_params_

        # 保存结果
        self.results[model_name] = metrics
        self.trained_models[model_name] = best_model

        print(f"   📊 测试结果: F1={metrics['f1']:.4f}, AUC={metrics['auc']:.4f}")

        return best_model, metrics

    def _calculate_metrics(self, y_true, y_pred, y_pred_proba=None):
        """计算评估指标"""
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, zero_division=0),
            'recall': recall_score(y_true, y_pred, zero_division=0),
            'f1': f1_score(y_true, y_pred, zero_division=0),
        }

        if y_pred_proba is not None:
            try:
                metrics['auc'] = roc_auc_score(y_true, y_pred_proba)
            except:
                metrics['auc'] = 0.0
        else:
            metrics['auc'] = 0.0

        return metrics

    def run_all_experiments(self):
        """运行所有模型实验"""
        print("🚀 开始机器学习模型对比实验")
        print("=" * 60)

        # 加载数据
        X_train, X_test, y_train, y_test = self.load_and_prepare_data()

        print(f"\n🔬 开始模型训练和评估...")
        print("=" * 60)

        # 运行所有模型
        for model_name, model_info in tqdm(self.model_collection.models.items(), desc="模型实验"):
            try:
                self.run_single_model(
                    model_name, model_info, X_train, X_test, y_train, y_test)
            except Exception as e:
                print(f"❌ {model_name} 实验失败: {e}")
                continue

        # 生成报告
        self.generate_report()

        # 保存结果
        self.save_results()

        # 可视化
        if SAVE_PLOTS:
            self.create_visualizations(X_train, X_test, y_train, y_test)

    def generate_report(self):
        """生成实验报告"""
        print(f"\n📊 实验结果汇总")
        print("=" * 80)

        # 创建结果DataFrame
        results_df = pd.DataFrame(self.results).T
        results_df = results_df.sort_values('f1', ascending=False)

        print("\n🏆 模型性能排名 (按F1分数排序):")
        print("-" * 80)
        print(
            f"{'模型':<15} {'准确率':<8} {'精确率':<8} {'召回率':<8} {'F1分数':<8} {'AUC':<8} {'CV-F1':<8}")
        print("-" * 80)

        for model_name, row in results_df.iterrows():
            print(f"{model_name:<15} {row['accuracy']:<8.4f} {row['precision']:<8.4f} "
                  f"{row['recall']:<8.4f} {row['f1']:<8.4f} {row['auc']:<8.4f} {row['cv_f1']:<8.4f}")

        print("-" * 80)

        # 找出最佳模型
        best_model_name = results_df.index[0]
        best_metrics = results_df.iloc[0]

        print(f"\n🎉 最佳模型: {best_model_name}")
        print(f"   F1分数: {best_metrics['f1']:.4f}")
        print(f"   AUC: {best_metrics['auc']:.4f}")
        print(f"   准确率: {best_metrics['accuracy']:.4f}")

        return results_df

    def save_results(self):
        """保存实验结果"""
        print(f"\n💾 保存实验结果到 {self.output_dir}")

        # 保存详细结果
        results_file = os.path.join(
            self.output_dir, "ml_comparison_results.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

        # 保存CSV格式结果
        results_df = pd.DataFrame(self.results).T
        csv_file = os.path.join(self.output_dir, "ml_comparison_results.csv")
        results_df.to_csv(csv_file, encoding='utf-8')

        # 保存模型 (如果启用)
        if SAVE_MODELS:
            models_dir = os.path.join(self.output_dir, "models")
            os.makedirs(models_dir, exist_ok=True)

            for model_name, model in self.trained_models.items():
                model_file = os.path.join(
                    models_dir, f"{model_name.replace(' ', '_')}.pkl")
                joblib.dump(model, model_file)

        print(f"✅ 结果保存完成")

    def create_visualizations(self, X_train, X_test, y_train, y_test):
        """创建可视化图表"""
        print(f"\n📈 生成可视化图表...")

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 1. 模型性能对比
        self._plot_model_comparison()

        # 2. 特征重要性 (针对树模型)
        self._plot_feature_importance()

        # 3. ROC曲线对比
        self._plot_roc_curves(X_test, y_test)

        print(f"✅ 可视化图表保存完成")

    def _plot_model_comparison(self):
        """绘制模型性能对比图"""
        results_df = pd.DataFrame(self.results).T

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('机器学习模型性能对比', fontsize=16)

        metrics = ['accuracy', 'precision', 'recall', 'f1']
        metric_names = ['准确率', '精确率', '召回率', 'F1分数']

        for i, (metric, name) in enumerate(zip(metrics, metric_names)):
            ax = axes[i//2, i % 2]

            data = results_df[metric].sort_values(ascending=True)
            bars = ax.barh(range(len(data)), data.values)
            ax.set_yticks(range(len(data)))
            ax.set_yticklabels(data.index)
            ax.set_xlabel(name)
            ax.set_title(f'{name}对比')

            # 添加数值标签
            for j, bar in enumerate(bars):
                width = bar.get_width()
                ax.text(width + 0.01, bar.get_y() + bar.get_height()/2,
                        f'{width:.3f}', ha='left', va='center')

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir,
                    'model_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_feature_importance(self):
        """绘制特征重要性图"""
        # 选择随机森林模型的特征重要性
        if 'Random Forest' in self.trained_models:
            model = self.trained_models['Random Forest']
            if hasattr(model, 'feature_importances_'):

                importances = model.feature_importances_
                indices = np.argsort(importances)[::-1][:20]  # 前20个重要特征

                plt.figure(figsize=(12, 8))
                plt.title('特征重要性排名 (随机森林)', fontsize=14)
                plt.bar(range(len(indices)), importances[indices])
                plt.xticks(range(len(indices)),
                           [self.data_processor.feature_names[i]
                               for i in indices],
                           rotation=45, ha='right')
                plt.ylabel('重要性')
                plt.tight_layout()
                plt.savefig(os.path.join(
                    self.output_dir, 'feature_importance.png'), dpi=300, bbox_inches='tight')
                plt.close()

    def _plot_roc_curves(self, X_test, y_test):
        """绘制ROC曲线对比"""
        plt.figure(figsize=(10, 8))

        for model_name, model in self.trained_models.items():
            if hasattr(model, 'predict_proba'):
                y_pred_proba = model.predict_proba(X_test)[:, 1]
                fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
                auc_score = self.results[model_name]['auc']
                plt.plot(fpr, tpr, label=f'{model_name} (AUC={auc_score:.3f})')

        plt.plot([0, 1], [0, 1], 'k--', label='随机猜测')
        plt.xlabel('假阳性率')
        plt.ylabel('真阳性率')
        plt.title('ROC曲线对比')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'roc_curves.png'),
                    dpi=300, bbox_inches='tight')
        plt.close()

# ==================== 主函数 ====================


def main():
    """主函数"""
    print("🎯 机器学习模型对比实验")
    print("=" * 60)
    print(f"📊 目标疾病: {TARGET_DISEASES}")
    print(f"📂 数据路径: {DATA_PATH}")
    print(f"📁 结果保存: {OUTPUT_DIR}")
    print(f"🔬 交叉验证: {CV_FOLDS} 折")
    print("=" * 60)

    # 创建实验运行器
    runner = MLExperimentRunner(TARGET_DISEASES, OUTPUT_DIR)

    try:
        # 运行实验
        runner.run_all_experiments()

        print(f"\n🎉 实验完成!")
        print(f"📁 结果保存在: {OUTPUT_DIR}")
        print(f"   - ml_comparison_results.json: 详细结果")
        print(f"   - ml_comparison_results.csv: 表格结果")
        if SAVE_MODELS:
            print(f"   - models/: 训练好的模型")
        if SAVE_PLOTS:
            print(f"   - *.png: 可视化图表")

    except Exception as e:
        print(f"❌ 实验失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
