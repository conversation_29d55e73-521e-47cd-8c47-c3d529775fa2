#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版多卡训练 - 基于原版训练代码
"""

# ==================== 配置区域 ====================

from dataset import load_unified_timeseries_data
from model import create_multimodal_qwen_model
import warnings
from torch.cuda.amp import autocast, GradScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import os
from tqdm import tqdm
import numpy as np
from torch.utils.data import DataLoader, WeightedRandomSampler
from torch.utils.data.distributed import DistributedSampler
import torch.optim as optim
import torch.nn.functional as F
import torch.nn as nn
import torch
import torch.distributed as dist
import torch.multiprocessing as mp
from torch.nn.parallel import DistributedDataParallel as DDP

# 🎮 多卡配置
USE_MULTI_GPU = True        # 是否使用多卡
GPU_IDS = [0, 1, 2, 3]     # 使用的GPU编号

# 🎯 疾病预测配置
TARGET_DISEASES = ['高血压', '脂肪肝', '糖尿病',
                   '高血脂', "支气管炎", "气管炎", "贫血", "肾囊肿", '冠心病']

# ⚖️ 数据不均衡处理策略配置
IMBALANCE_STRATEGY = 'focal_loss'
FOCAL_ALPHA = 0.1
FOCAL_GAMMA = 3.0
POS_WEIGHT = 200.0
OVERSAMPLE_RATIO = 0.1

# 🎯 预测阈值配置
PREDICTION_THRESHOLD = 0.5
DYNAMIC_THRESHOLD = True
EARLY_STOPPING_PATIENCE = 30

# 📝 特征选择配置
TEXT_FEATURES = [
    '症状', '既往史', '家族史', '性别', '年龄num',
    '职业', '吸烟状况', '饮酒频率', '饮食习惯'
]

NUMERIC_FEATURES = [
    '体温', '脉率', '呼吸频率', '左侧收缩压', '左侧舒张压',
    '右侧收缩压', '右侧舒张压', '身高', '体重', '腰围', '体质指数',
    '血红蛋白', '白细胞', '血小板', '空腹血糖MMOL',
    '总胆固醇', '甘油三酯', '血清肌酐', '血尿素'
]

# 📂 数据配置
DATA_PATH = "/root/work_speace/prediction/coronary_timeseries_data_fixed.csv"
MAX_SAMPLES = None
PREDICTION_MODE = 'binary'

# 🚀 训练配置
QWEN_MODEL_NAME = '/root/work_speace/prediction/Qwen2.5-0.5B'
LEARNING_RATE = 1e-5
BATCH_SIZE = 32  # 每个GPU的batch size
EPOCHS = 10
USE_MIXED_PRECISION = True

# 💾 保存配置
SAVE_PATH = f"qwen_medical_model_multigpu.pth"

# ==================== 训练代码 ====================

warnings.filterwarnings('ignore')
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'


class FocalLoss(nn.Module):
    """Focal Loss - 专门处理极端不均衡数据"""

    def __init__(self, alpha=0.25, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        ce_loss = F.binary_cross_entropy_with_logits(
            inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        alpha_t = self.alpha * targets + (1 - self.alpha) * (1 - targets)
        focal_loss = alpha_t * (1 - pt) ** self.gamma * ce_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class WeightedBCELoss(nn.Module):
    """加权BCE Loss"""

    def __init__(self, pos_weight):
        super(WeightedBCELoss, self).__init__()
        self.pos_weight = pos_weight

    def forward(self, inputs, targets):
        return F.binary_cross_entropy_with_logits(
            inputs, targets,
            pos_weight=torch.tensor(self.pos_weight).to(inputs.device)
        )


class MultiGPUQwenTrainer:
    """多卡Qwen医疗预测训练器"""

    def __init__(self, rank=0, world_size=1):
        self.rank = rank
        self.world_size = world_size
        self.device = torch.device(f'cuda:{rank}')
        self.scaler = GradScaler() if USE_MIXED_PRECISION else None

        if rank == 0:  # 只在主进程打印
            print(f"🎮 多卡配置: {world_size} GPUs")
            print(f"🎯 疾病: {TARGET_DISEASES}")
            print(f"🚀 每GPU批次: {BATCH_SIZE}, 总批次: {BATCH_SIZE * world_size}")

    def analyze_data_distribution(self, dataset):
        """分析数据分布 - 支持多疾病"""
        labels = []
        for i in range(len(dataset)):
            label = dataset[i]['label']

            if label.dim() == 0:  # 标量
                labels.append(label.item())
            elif label.dim() == 1:  # 向量（多疾病）
                labels.append(label.cpu().numpy())
            else:
                raise ValueError(f"不支持的标签维度: {label.dim()}")

        # 处理不同情况
        if isinstance(labels[0], (int, float)):
            # 单疾病情况
            labels = np.array(labels)
            pos_count = (labels == 1).sum()
            neg_count = (labels == 0).sum()
            total = len(labels)
            pos_ratio = pos_count / total

            if self.rank == 0:
                print(f"📊 数据分布分析 (单疾病):")
                print(f"   总样本: {total:,}")
                print(f"   阳性样本: {pos_count:,} ({pos_ratio*100:.3f}%)")
                print(f"   阴性样本: {neg_count:,} ({(1-pos_ratio)*100:.3f}%)")
                print(
                    f"   不均衡比例: 1:{neg_count//pos_count if pos_count > 0 else 'inf'}")

            return pos_ratio, labels
        else:
            # 多疾病情况
            labels = np.array(labels)
            total = len(labels)

            if self.rank == 0:
                print(f"📊 数据分布分析 (多疾病):")
                print(f"   总样本: {total:,}")
                print(f"   疾病数量: {len(TARGET_DISEASES)}")

                for i, disease in enumerate(TARGET_DISEASES):
                    pos_count = (labels[:, i] == 1).sum()
                    pos_ratio = pos_count / total
                    print(
                        f"   {disease}: {pos_count:,}/{total:,} ({pos_ratio*100:.3f}%)")

            representative_ratio = (labels[:, 0] == 1).sum() / total
            return representative_ratio, labels

    def create_balanced_sampler(self, labels):
        """创建平衡采样器 - 支持多疾病"""
        if IMBALANCE_STRATEGY not in ['oversample', 'undersample']:
            return None

        if self.rank == 0:
            print(f"🔄 创建平衡采样器 ({IMBALANCE_STRATEGY}):")

        # 处理多疾病情况 - 使用第一个疾病作为采样依据
        if labels.ndim == 2:
            sampling_labels = labels[:, 0]
            if self.rank == 0:
                print(f"   使用 {TARGET_DISEASES[0]} 作为采样依据")
        else:
            sampling_labels = labels

        pos_indices = np.where(sampling_labels == 1)[0]
        neg_indices = np.where(sampling_labels == 0)[0]

        if IMBALANCE_STRATEGY == 'oversample':
            target_pos_count = int(len(labels) * OVERSAMPLE_RATIO)
            oversample_factor = max(
                1, target_pos_count // len(pos_indices)) if len(pos_indices) > 0 else 1

            weights = np.ones(len(labels))
            if len(pos_indices) > 0:
                weights[pos_indices] = oversample_factor

            if self.rank == 0:
                print(f"   正样本过采样倍数: {oversample_factor}x")
                print(f"   目标正样本比例: {OVERSAMPLE_RATIO*100:.1f}%")
        else:  # undersample
            target_neg_count = len(
                pos_indices) * 10 if len(pos_indices) > 0 else len(neg_indices)
            undersample_factor = max(
                0.1, target_neg_count / len(neg_indices)) if len(neg_indices) > 0 else 1.0

            weights = np.ones(len(labels))
            if len(neg_indices) > 0:
                weights[neg_indices] = undersample_factor

            if self.rank == 0:
                print(f"   负样本欠采样权重: {undersample_factor:.3f}")

        sampler = WeightedRandomSampler(
            weights=weights,
            num_samples=len(labels),
            replacement=True
        )
        return sampler

    def find_optimal_threshold(self, probs, labels):
        """找到最优预测阈值以最大化F1分数 - 支持多疾病"""
        if not DYNAMIC_THRESHOLD:
            return PREDICTION_THRESHOLD

        # 处理多疾病情况
        if isinstance(probs, torch.Tensor):
            probs = probs.cpu().numpy()
        if isinstance(labels, torch.Tensor):
            labels = labels.cpu().numpy()

        # 如果是多疾病，使用第一个疾病
        if len(probs.shape) > 1 and probs.shape[1] > 1:
            probs_for_threshold = probs[:, 0]
            labels_for_threshold = labels[:, 0] if len(
                labels.shape) > 1 else labels
            if self.rank == 0:
                print(f"   使用 {TARGET_DISEASES[0]} 优化阈值")
        else:
            probs_for_threshold = probs.flatten() if len(probs.shape) > 1 else probs
            labels_for_threshold = labels.flatten() if len(labels.shape) > 1 else labels

        best_f1 = 0
        best_threshold = PREDICTION_THRESHOLD

        # 测试不同阈值
        thresholds = np.arange(0.1, 0.9, 0.05)

        for threshold in thresholds:
            preds = (probs_for_threshold > threshold).astype(int)

            if len(np.unique(preds)) > 1:  # 确保有正负预测
                f1 = f1_score(labels_for_threshold, preds, zero_division=0)
                if f1 > best_f1:
                    best_f1 = f1
                    best_threshold = threshold

        return best_threshold

    def add_prediction_bias(self):
        """给模型输出层添加正向偏置，鼓励预测阳性"""
        if self.rank == 0:
            print(f"🎯 添加预测偏置以鼓励阳性预测...")

        # 找到最后的分类层
        model_to_check = self.model.module if hasattr(
            self.model, 'module') else self.model

        if hasattr(model_to_check, 'classifier'):
            classifier = model_to_check.classifier
        elif hasattr(model_to_check, 'disease_decoder'):
            classifier = model_to_check.disease_decoder
        else:
            if self.rank == 0:
                print("   ⚠️ 未找到分类层，跳过偏置设置")
            return

        # 为最后一层添加正向偏置
        if isinstance(classifier, nn.Sequential):
            last_layer = classifier[-1]
        else:
            last_layer = classifier

        if hasattr(last_layer, 'bias') and last_layer.bias is not None:
            with torch.no_grad():
                positive_bias = np.log(0.01 / 0.99)  # 对应1%的先验概率
                last_layer.bias.fill_(positive_bias)
            if self.rank == 0:
                print(f"   ✅ 设置分类偏置: {positive_bias:.3f}")
        else:
            if self.rank == 0:
                print(f"   ⚠️ 分类层没有偏置项")

    def get_loss_function(self):
        """根据策略选择损失函数"""
        if IMBALANCE_STRATEGY == 'focal_loss':
            if self.rank == 0:
                print(
                    f"📐 使用Focal Loss (alpha={FOCAL_ALPHA}, gamma={FOCAL_GAMMA})")
            return FocalLoss(alpha=FOCAL_ALPHA, gamma=FOCAL_GAMMA)
        elif IMBALANCE_STRATEGY == 'weighted_loss':
            if self.rank == 0:
                print(f"⚖️ 使用加权BCE Loss (pos_weight={POS_WEIGHT})")
            return WeightedBCELoss(pos_weight=POS_WEIGHT)
        else:
            if self.rank == 0:
                print(f"📊 使用标准BCE Loss")
            return nn.BCEWithLogitsLoss()

    def create_model(self):
        """创建模型"""
        if self.rank == 0:
            print(f"🤖 创建模型...")

        self.model = create_multimodal_qwen_model(
            target_diseases=TARGET_DISEASES,
            text_features=TEXT_FEATURES,
            numeric_features=NUMERIC_FEATURES,
            qwen_model_name=QWEN_MODEL_NAME,
            prediction_mode=PREDICTION_MODE
        ).to(self.device)

        # 包装为DDP模型
        if self.world_size > 1:
            self.model = DDP(self.model, device_ids=[self.rank])

        # 优化器和损失函数
        self.optimizer = optim.AdamW(self.model.parameters(), lr=LEARNING_RATE)
        self.criterion = self.get_loss_function()

        # 添加预测偏置
        self.add_prediction_bias()

        if self.rank == 0:
            print(f"✅ 模型创建完成")

    def convert_batch_to_patient_data(self, batch):
        """将batch转换为patient_data格式"""
        texts = batch['text']
        numeric = batch['numeric'].to(self.device)
        labels = batch['label'].to(self.device)

        patient_data = []
        for i in range(len(texts)):
            patient_dict = {}

            text = texts[i]
            if "年龄" in text:
                try:
                    age = text.split("年龄")[1].split("岁")[0].strip(": ")
                    patient_dict['年龄num'] = float(age)
                except:
                    patient_dict['年龄num'] = 0

            if "症状:" in text:
                try:
                    symptom = text.split("症状:")[1].split()[0]
                    patient_dict['症状'] = symptom
                except:
                    patient_dict['症状'] = '无症状'

            # 添加数值特征
            numeric_values = numeric[i].cpu().numpy()
            for j, feature_name in enumerate(NUMERIC_FEATURES):
                if j < len(numeric_values):
                    patient_dict[feature_name] = float(numeric_values[j])

            patient_data.append(patient_dict)

        return patient_data, labels

    def train_epoch(self, train_loader, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0

        # 设置分布式采样器的epoch
        if hasattr(train_loader.sampler, 'set_epoch'):
            train_loader.sampler.set_epoch(epoch)

        if self.rank == 0:
            pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}")
        else:
            pbar = train_loader

        for step, batch in enumerate(pbar):
            patient_data, labels = self.convert_batch_to_patient_data(batch)
            self.optimizer.zero_grad()

            if USE_MIXED_PRECISION:
                with autocast():
                    probs, logits = self.model(patient_data)
                    loss = self.criterion(logits, labels.float())

                self.scaler.scale(loss).backward()
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                probs, logits = self.model(patient_data)
                loss = self.criterion(logits, labels.float())
                loss.backward()
                self.optimizer.step()

            total_loss += loss.item()

            if self.rank == 0:
                pbar.set_postfix({'loss': f'{loss.item():.4f}'})

        return total_loss / len(train_loader)

    def validate_epoch(self, val_loader):
        """验证epoch - 支持多疾病"""
        self.model.eval()
        total_loss = 0
        all_preds = []
        all_labels = []
        all_probs = []

        with torch.no_grad():
            if self.rank == 0:
                pbar = tqdm(val_loader, desc="Validating")
            else:
                pbar = val_loader

            for batch in pbar:
                patient_data, labels = self.convert_batch_to_patient_data(
                    batch)

                if USE_MIXED_PRECISION:
                    with autocast():
                        probs, logits = self.model(patient_data)
                        loss = self.criterion(logits, labels.float())
                else:
                    probs, logits = self.model(patient_data)
                    loss = self.criterion(logits, labels.float())

                total_loss += loss.item()
                all_probs.append(probs.cpu())
                all_labels.append(labels.cpu())

        # 合并结果
        all_probs = torch.cat(all_probs, dim=0)
        all_labels = torch.cat(all_labels, dim=0)

        # 计算最优阈值
        if DYNAMIC_THRESHOLD:
            optimal_threshold = self.find_optimal_threshold(
                all_probs, all_labels)
            if self.rank == 0:
                print(
                    f"🎯 最优阈值: {optimal_threshold:.3f} (默认: {PREDICTION_THRESHOLD})")
        else:
            optimal_threshold = PREDICTION_THRESHOLD
            if self.rank == 0:
                print(f"🎯 使用固定阈值: {PREDICTION_THRESHOLD}")

        # 根据阈值计算预测结果
        all_preds = (all_probs > optimal_threshold).float()

        # 计算指标
        avg_loss = total_loss / len(val_loader)

        if len(TARGET_DISEASES) == 1:
            # 单疾病指标计算
            preds_np = all_preds.numpy().flatten()
            labels_np = all_labels.numpy().flatten()
            probs_np = all_probs.numpy().flatten()

            acc = accuracy_score(labels_np, preds_np)
            precision = precision_score(labels_np, preds_np, zero_division=0)
            recall = recall_score(labels_np, preds_np, zero_division=0)
            f1 = f1_score(labels_np, preds_np, zero_division=0)

            try:
                auc = roc_auc_score(labels_np, probs_np)
            except:
                auc = 0.0

            pred_pos = int(preds_np.sum())
            pred_neg = len(preds_np) - pred_pos
            true_pos = int(labels_np.sum())
            true_neg = len(labels_np) - true_pos
        else:
            # 多疾病指标计算 - 使用第一个疾病作为代表
            preds_np = all_preds[:, 0].numpy()
            labels_np = all_labels[:, 0].numpy()
            probs_np = all_probs[:, 0].numpy()

            acc = accuracy_score(labels_np, preds_np)
            precision = precision_score(labels_np, preds_np, zero_division=0)
            recall = recall_score(labels_np, preds_np, zero_division=0)
            f1 = f1_score(labels_np, preds_np, zero_division=0)

            try:
                auc = roc_auc_score(labels_np, probs_np)
            except:
                auc = 0.0

            pred_pos = int(preds_np.sum())
            pred_neg = len(preds_np) - pred_pos
            true_pos = int(labels_np.sum())
            true_neg = len(labels_np) - true_pos

            # 打印每个疾病的详细指标（只在主进程）
            if self.rank == 0:
                print(f"📊 各疾病详细指标:")
                for i, disease in enumerate(TARGET_DISEASES):
                    disease_preds = all_preds[:, i].numpy()
                    disease_labels = all_labels[:, i].numpy()
                    disease_f1 = f1_score(
                        disease_labels, disease_preds, zero_division=0)
                    disease_pos = int(disease_preds.sum())
                    disease_true_pos = int(disease_labels.sum())
                    print(
                        f"   {disease}: F1={disease_f1:.3f}, 预测阳性={disease_pos}, 真实阳性={disease_true_pos}")

        return {
            'val_loss': avg_loss,
            'accuracy': acc,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'auc': auc,
            'pred_positive': pred_pos,
            'pred_negative': pred_neg,
            'true_positive': true_pos,
            'true_negative': true_neg
        }

    def train(self, train_dataset, val_dataset):
        """训练模型"""
        if self.rank == 0:
            print(f"🚀 开始多卡训练...")

        # 分析数据分布
        pos_ratio, train_labels = self.analyze_data_distribution(train_dataset)

        # 创建平衡采样器
        balanced_sampler = self.create_balanced_sampler(train_labels)

        # 创建数据加载器
        if self.world_size > 1:
            # 多卡情况下，如果有balanced_sampler，需要特殊处理
            if balanced_sampler is not None and IMBALANCE_STRATEGY in ['oversample', 'undersample']:
                # 对于多卡+不均衡采样，我们使用DistributedSampler但依然使用损失函数处理不均衡
                train_sampler = DistributedSampler(train_dataset, shuffle=True)
                if self.rank == 0:
                    print("⚠️ 多卡训练时不均衡处理主要依赖损失函数")
            else:
                train_sampler = DistributedSampler(train_dataset, shuffle=True)
            val_sampler = DistributedSampler(val_dataset, shuffle=False)
        else:
            # 单卡情况
            if balanced_sampler is not None:
                train_sampler = balanced_sampler
            else:
                train_sampler = None
            val_sampler = None

        train_loader = DataLoader(
            train_dataset,
            batch_size=BATCH_SIZE,
            shuffle=(train_sampler is None),
            sampler=train_sampler,
            num_workers=4,
            pin_memory=True
        )

        val_loader = DataLoader(
            val_dataset,
            batch_size=BATCH_SIZE,
            shuffle=False,
            sampler=val_sampler,
            num_workers=4,
            pin_memory=True
        )

        # 创建模型
        self.create_model()

        best_f1 = 0.0
        best_auc = 0.0
        patience_counter = 0

        for epoch in range(EPOCHS):
            if self.rank == 0:
                print(f"\n=== Epoch {epoch+1}/{EPOCHS} ===")

            # 训练
            train_loss = self.train_epoch(train_loader, epoch)

            # 验证
            val_metrics = self.validate_epoch(val_loader)

            # 只在主进程保存和打印
            if self.rank == 0:
                print(f"📊 训练损失: {train_loss:.4f}")
                print(f"📊 验证损失: {val_metrics['val_loss']:.4f}")
                print(f"📊 准确率: {val_metrics['accuracy']:.4f}")
                print(f"📊 精确率: {val_metrics['precision']:.4f}")
                print(f"📊 召回率: {val_metrics['recall']:.4f}")
                print(f"📊 F1分数: {val_metrics['f1']:.4f}")
                print(f"📊 AUC: {val_metrics['auc']:.4f}")

                # 预测分布
                total_samples = val_metrics['pred_positive'] + \
                    val_metrics['pred_negative']
                total_true = val_metrics['true_positive'] + \
                    val_metrics['true_negative']
                pred_pos_rate = val_metrics['pred_positive'] / \
                    total_samples * 100 if total_samples > 0 else 0
                true_pos_rate = val_metrics['true_positive'] / \
                    total_true * 100 if total_true > 0 else 0

                print(f"🎯 预测分布:")
                print(
                    f"   预测阳性: {val_metrics['pred_positive']}/{total_samples} ({pred_pos_rate:.2f}%)")
                print(
                    f"   真实阳性: {val_metrics['true_positive']}/{total_true} ({true_pos_rate:.2f}%)")

                current_f1 = val_metrics['f1']
                current_auc = val_metrics['auc']

                # 检查是否为最佳模型
                improved = False
                if current_f1 > best_f1:
                    improved = True
                elif current_f1 == best_f1 and current_auc > best_auc:
                    improved = True

                if improved:
                    best_f1 = current_f1
                    best_auc = current_auc
                    patience_counter = 0

                    # 保存模型状态
                    model_state = self.model.module.state_dict() if hasattr(
                        self.model, 'module') else self.model.state_dict()

                    torch.save({
                        'model_state_dict': model_state,
                        'optimizer_state_dict': self.optimizer.state_dict(),
                        'epoch': epoch + 1,
                        'f1': best_f1,
                        'auc': best_auc,
                        'strategy': IMBALANCE_STRATEGY,
                        'threshold': PREDICTION_THRESHOLD,
                        'config': {
                            'target_diseases': TARGET_DISEASES,
                            'qwen_model': QWEN_MODEL_NAME
                        }
                    }, SAVE_PATH)
                    print(f"🎉 新的最佳模型! F1: {best_f1:.4f}, AUC: {best_auc:.4f}")
                else:
                    patience_counter += 1
                    print(
                        f"⏳ 未改善 ({patience_counter}/{EARLY_STOPPING_PATIENCE})")

                if patience_counter >= EARLY_STOPPING_PATIENCE:
                    print(f"🛑 早停! 连续{EARLY_STOPPING_PATIENCE}轮未改善")
                    break

        if self.rank == 0:
            print(f"\n✅ 训练完成!")
            print(f"📈 最佳F1: {best_f1:.4f}")
            print(f"📈 最佳AUC: {best_auc:.4f}")

            if best_f1 == 0:
                print(f"⚠️ F1分数仍为0，建议:")
                print(f"   1. 降低PREDICTION_THRESHOLD到0.1-0.2")
                print(f"   2. 增加FOCAL_ALPHA到0.05")
                print(f"   3. 增加POS_WEIGHT到500+")

        return {'best_f1': best_f1, 'best_auc': best_auc}


def setup_distributed(rank, world_size):
    """设置分布式训练"""
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = '12355'
    dist.init_process_group("nccl", rank=rank, world_size=world_size)
    torch.cuda.set_device(rank)


def cleanup():
    """清理分布式训练"""
    dist.destroy_process_group()


def run_worker(rank, world_size):
    """工作进程"""
    setup_distributed(rank, world_size)

    # 加载数据
    train_dataset, val_dataset, test_dataset, scaler, feature_info = load_unified_timeseries_data(
        csv_path=DATA_PATH,
        target_diseases=TARGET_DISEASES,
        prediction_mode=PREDICTION_MODE,
        test_size=0.2,
        max_samples=MAX_SAMPLES,
        time_gap_years=1
    )

    if train_dataset is None:
        cleanup()
        return

    # 训练
    trainer = MultiGPUQwenTrainer(rank=rank, world_size=world_size)
    trainer.train(train_dataset, val_dataset)

    cleanup()


def main():
    """主函数"""
    print(f"🎯 多卡Qwen医疗预测训练")
    print(f"📱 可用GPU: {torch.cuda.device_count()}")

    if USE_MULTI_GPU and len(GPU_IDS) > 1 and torch.cuda.is_available():
        world_size = len(GPU_IDS)
        print(f"🚀 启动多卡训练: {world_size} GPUs")
        mp.spawn(run_worker, args=(world_size,), nprocs=world_size, join=True)
    else:
        print(f"🚀 启动单卡训练")

        # 单卡训练
        train_dataset, val_dataset, test_dataset, scaler, feature_info = load_unified_timeseries_data(
            csv_path=DATA_PATH,
            target_diseases=TARGET_DISEASES,
            prediction_mode=PREDICTION_MODE,
            test_size=0.2,
            max_samples=MAX_SAMPLES,
            time_gap_years=1
        )

        if train_dataset is None:
            print(f"❌ 数据加载失败")
            return

        trainer = MultiGPUQwenTrainer(rank=0, world_size=1)
        try:
            results = trainer.train(train_dataset, val_dataset)
            print(f"\n🎉 单卡训练成功!")
            print(f"💾 模型保存: {SAVE_PATH}")
        except Exception as e:
            print(f"❌ 训练失败: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()
