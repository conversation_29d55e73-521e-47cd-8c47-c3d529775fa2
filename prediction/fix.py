import pandas as pd


def fix_csv_first_line(broken_csv_path, reference_csv_path, output_csv_path):
    """
    简单粗暴：只替换CSV的第一行（列名）
    """

    print(f"🔧 替换CSV第一行（列名）...")
    print(f"   损坏文件: {broken_csv_path}")
    print(f"   参考文件: {reference_csv_path}")
    print(f"   输出文件: {output_csv_path}")

    try:
        # 1. 读取参考文件的第一行（列名）
        with open(reference_csv_path, 'r', encoding='utf-8') as f:
            correct_header = f.readline().strip()

        print(f"✅ 获取到正确的列名行")
        print(f"   列名预览: {correct_header[:100]}...")

        # 2. 读取损坏文件的所有行
        with open(broken_csv_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        print(f"📊 读取损坏文件: {len(lines)} 行")
        print(f"   原第一行: {lines[0][:100]}...")

        # 3. 替换第一行
        lines[0] = correct_header + '\n'

        # 4. 写入新文件
        with open(output_csv_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)

        print(f"✅ 替换完成!")
        print(f"   新第一行: {lines[0][:100]}...")

        # 5. 验证结果
        test_df = pd.read_csv(output_csv_path, delimiter='\t', nrows=2)
        print(f"\n📋 验证结果:")
        print(f"   列数: {len(test_df.columns)}")
        print(f"   前5列: {test_df.columns.tolist()[:5]}")

        # 检查关键列
        key_cols = ['uniq_ID', '年份', '冠心病']
        found_cols = [col for col in key_cols if col in test_df.columns]
        missing_cols = [col for col in key_cols if col not in test_df.columns]

        print(f"   ✅ 找到关键列: {found_cols}")
        if missing_cols:
            print(f"   ❌ 缺少关键列: {missing_cols}")

        return True

    except Exception as e:
        print(f"❌ 替换失败: {e}")
        return False


def main():
    """主函数"""

    # 🔥 配置文件路径 - 只需要修改这三行
    BROKEN_CSV = "/root/work_speace/prediction/coronary_timeseries_data.csv"  # 列名损坏的文件
    REFERENCE_CSV = "/root/work_speace/prediction/4-total_3years-uniqPerson_clean.csv"  # 有正确列名的参考文件
    OUTPUT_CSV = "coronary_timeseries_data_fixed.csv"  # 修复后的文件

    print(f"🛠️ 简单CSV列名修复工具")
    print(f"=" * 50)

    success = fix_csv_first_line(BROKEN_CSV, REFERENCE_CSV, OUTPUT_CSV)

    if success:
        print(f"\n🎉 修复成功!")
        print(f"📁 修复后文件: {OUTPUT_CSV}")
        print(f"💡 现在可以在训练代码中使用这个文件了")

        # 更新训练代码中的路径提示
        print(f"\n📝 记得在训练代码中修改:")
        print(f'   DATA_PATH = "{OUTPUT_CSV}"')
    else:
        print(f"\n❌ 修复失败")


if __name__ == "__main__":
    main()
