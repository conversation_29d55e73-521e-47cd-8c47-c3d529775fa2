import pandas as pd
import sys
import os

def preview_csv(file_path, n_rows=10, show_columns=True, show_stats=True):
    """
    预览CSV文件的前几行数据
    
    Args:
        file_path: CSV文件路径
        n_rows: 显示行数，默认10行
        show_columns: 是否显示列信息
        show_stats: 是否显示基本统计
    """
    
    print(f"📁 预览文件: {file_path}")
    print("=" * 80)
    
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return
        
        # 获取文件大小
        file_size_mb = os.path.getsize(file_path) / 1024 / 1024
        print(f"📊 文件大小: {file_size_mb:.1f} MB")
        
        # 尝试不同的分隔符
        separators = ['\t', ',', ';', '|']
        df = None
        used_sep = None
        
        for sep in separators:
            try:
                df = pd.read_csv(file_path, nrows=n_rows+1, sep=sep, encoding='utf-8')
                if len(df.columns) > 1:  # 至少有2列才认为分隔符正确
                    used_sep = sep
                    break
            except:
                continue
        
        if df is None:
            print("❌ 无法读取文件，请检查文件格式")
            return
        
        sep_names = {'\t': '制表符', ',': '逗号', ';': '分号', '|': '竖线'}
        print(f"🔍 使用分隔符: {sep_names.get(used_sep, used_sep)}")
        
        # 显示基本信息
        print(f"📋 总列数: {len(df.columns)}")
        print(f"📄 预览行数: {min(n_rows, len(df))}")
        
        # 显示列信息
        if show_columns:
            print(f"\n📑 列信息:")
            for i, col in enumerate(df.columns):
                col_type = str(df[col].dtype)
                sample_val = str(df[col].iloc[0]) if len(df) > 0 else 'N/A'
                if len(sample_val) > 20:
                    sample_val = sample_val[:20] + "..."
                print(f"   {i+1:2d}. {col:<30} | {col_type:<12} | 样例: {sample_val}")
        
        # 显示前N行数据
        print(f"\n📄 前{min(n_rows, len(df))}行数据:")
        print("-" * 80)
        
        # 设置pandas显示选项
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', 30)
        
        print(df.head(n_rows))
        
        # 显示基本统计
        if show_stats and len(df) > 0:
            print(f"\n📊 基本统计:")
            
            # ID列统计
            id_column = df.columns[0]
            unique_ids = df[id_column].nunique()
            total_rows = len(df)
            print(f"   ID列 ({id_column}): {unique_ids} 个唯一值 / {total_rows} 行")
            
            # 数值列统计
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                print(f"   数值列数量: {len(numeric_cols)}")
                for col in numeric_cols[:5]:  # 只显示前5个数值列
                    col_stats = df[col].describe()
                    print(f"   {col}: 均值={col_stats['mean']:.1f}, 范围=[{col_stats['min']:.1f}, {col_stats['max']:.1f}]")
            
            # 文本列统计
            text_cols = df.select_dtypes(include=['object']).columns
            if len(text_cols) > 0:
                print(f"   文本列数量: {len(text_cols)}")
        
    except Exception as e:
        print(f"❌ 读取失败: {e}")

def main():
    """主函数 - 支持命令行参数"""
    
    # 默认文件路径
    default_file = "/root/work_speace/prediction/medical_simple.tsv"
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        n_rows = int(sys.argv[2]) if len(sys.argv) > 2 else 10
    else:
        file_path = default_file
        n_rows = 10
    
    print("🔍 CSV数据预览工具")
    print("=" * 80)
    
    preview_csv(file_path, n_rows=n_rows)
    
    print("\n" + "=" * 80)
    print("💡 使用方法:")
    print(f"   python {sys.argv[0]} <文件路径> [行数]")
    print(f"   例如: python {sys.argv[0]} data.csv 20")

if __name__ == "__main__":
    main()