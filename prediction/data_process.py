import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import psutil
import os
from datetime import datetime
from tqdm import tqdm
import gc
import threading
import queue
import time
from concurrent.futures import ThreadPoolExecutor

def get_memory_usage():
    """获取当前内存使用情况"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024  # MB

class MultiThreadTimeSeriesExtractor:
    """多线程时序数据提取器 - 生产者消费者模式"""
    
    def __init__(self, input_file, output_file, chunk_size=50000, queue_maxsize=10):
        self.input_file = input_file
        self.output_file = output_file
        self.chunk_size = chunk_size
        self.queue_maxsize = queue_maxsize
        
        # 线程间通信
        self.data_queue = queue.Queue(maxsize=queue_maxsize)
        self.patient_records = defaultdict(list)
        self.total_rows_read = 0
        self.total_patients = 0
        
        # 控制标志
        self.reading_finished = threading.Event()
        self.processing_finished = threading.Event()
        
        # 统计信息
        self.stats = {
            'chunks_read': 0,
            'chunks_processed': 0,
            'rows_read': 0,
            'rows_processed': 0,
            'patients_found': 0
        }
        
        # 线程锁
        self.stats_lock = threading.Lock()
        self.patient_lock = threading.Lock()
        
    def producer_thread(self):
        """生产者线程：读取CSV文件并放入队列"""
        print(f"🔄 [生产者] 开始读取文件...")
        start_time = time.time()
        
        try:
            chunk_reader = pd.read_csv(
                self.input_file,
                chunksize=self.chunk_size,
                delimiter='\t',
                encoding='utf-8',
                on_bad_lines='skip',
                engine='python'
            )
            
            for chunk_idx, chunk in enumerate(chunk_reader):
                # 将chunk放入队列（如果队列满了会阻塞）
                self.data_queue.put(('chunk', chunk, chunk_idx))
                
                # 更新统计
                with self.stats_lock:
                    self.stats['chunks_read'] += 1
                    self.stats['rows_read'] += len(chunk)
                
                # 每10个chunk显示进度
                if (chunk_idx + 1) % 10 == 0:
                    elapsed = time.time() - start_time
                    with self.stats_lock:
                        rows_read = self.stats['rows_read']
                        chunks_read = self.stats['chunks_read']
                    
                    speed = rows_read / elapsed if elapsed > 0 else 0
                    queue_size = self.data_queue.qsize()
                    
                    print(f"📖 [生产者] chunk {chunks_read}: {rows_read:,} 行 | "
                          f"速度: {speed:.0f} 行/s | 队列: {queue_size}/{self.queue_maxsize} | "
                          f"用时: {elapsed:.1f}s")
            
            # 发送结束信号
            self.data_queue.put(('end', None, None))
            self.reading_finished.set()
            
            total_time = time.time() - start_time
            print(f"✅ [生产者] 读取完成! 用时: {total_time:.1f}s")
            
        except Exception as e:
            print(f"❌ [生产者] 读取失败: {e}")
            self.data_queue.put(('error', str(e), None))
            
    def consumer_thread(self):
        """消费者线程：处理数据并构建hash表"""
        print(f"🔧 [消费者] 开始处理数据...")
        start_time = time.time()
        columns = None
        
        try:
            while True:
                try:
                    # 从队列获取数据（超时避免死锁）
                    message_type, data, chunk_idx = self.data_queue.get(timeout=30)
                    
                    if message_type == 'end':
                        print(f"📥 [消费者] 收到结束信号")
                        break
                    elif message_type == 'error':
                        print(f"❌ [消费者] 收到错误信号: {data}")
                        break
                    elif message_type == 'chunk':
                        # 处理chunk数据
                        chunk = data
                        
                        # 获取列名（第一次）
                        if columns is None:
                            columns = chunk.columns.tolist()
                            self.id_column = columns[0]
                            print(f"📋 [消费者] 检测到列结构: ID={self.id_column}, 总列数={len(columns)}")
                        
                        # 处理每一行
                        rows_processed = 0
                        patients_added = 0
                        
                        for _, row in chunk.iterrows():
                            patient_id = str(row.iloc[0])
                            if patient_id and patient_id not in ['nan', '', 'None']:
                                # 线程安全地添加患者记录
                                with self.patient_lock:
                                    old_count = len(self.patient_records[patient_id])
                                    self.patient_records[patient_id].append(row.values.tolist())
                                    if old_count == 0:
                                        patients_added += 1
                                
                                rows_processed += 1
                        
                        # 更新统计
                        with self.stats_lock:
                            self.stats['chunks_processed'] += 1
                            self.stats['rows_processed'] += rows_processed
                            self.stats['patients_found'] = len(self.patient_records)
                        
                        # 标记任务完成
                        self.data_queue.task_done()
                        
                        # 显示进度
                        if chunk_idx % 10 == 0:
                            elapsed = time.time() - start_time
                            with self.stats_lock:
                                chunks_proc = self.stats['chunks_processed']
                                rows_proc = self.stats['rows_processed']
                                patients = self.stats['patients_found']
                            
                            speed = rows_proc / elapsed if elapsed > 0 else 0
                            memory = get_memory_usage()
                            
                            print(f"🔧 [消费者] chunk {chunks_proc}: {rows_proc:,} 行 | "
                                  f"患者: {patients:,} | 速度: {speed:.0f} 行/s | "
                                  f"内存: {memory:.0f}MB | 用时: {elapsed:.1f}s")
                    
                except queue.Empty:
                    if self.reading_finished.is_set():
                        print(f"⏰ [消费者] 队列为空且读取已完成，退出处理")
                        break
                    else:
                        print(f"⏳ [消费者] 队列为空，等待数据...")
                        continue
                        
        except Exception as e:
            print(f"❌ [消费者] 处理失败: {e}")
        
        self.processing_finished.set()
        total_time = time.time() - start_time
        print(f"✅ [消费者] 处理完成! 用时: {total_time:.1f}s")
        
    def monitor_thread(self):
        """监控线程：显示整体进度"""
        print(f"📊 [监控] 开始监控进度...")
        start_time = time.time()
        
        while not (self.reading_finished.is_set() and self.processing_finished.is_set()):
            time.sleep(5)  # 每5秒更新一次
            
            elapsed = time.time() - start_time
            memory = get_memory_usage()
            
            with self.stats_lock:
                stats_copy = self.stats.copy()
            
            queue_size = self.data_queue.qsize()
            
            print(f"📊 [监控] 读取: {stats_copy['chunks_read']} chunks | "
                  f"处理: {stats_copy['chunks_processed']} chunks | "
                  f"患者: {stats_copy['patients_found']:,} | "
                  f"队列: {queue_size} | 内存: {memory:.0f}MB | "
                  f"总用时: {elapsed:.1f}s")
        
        print(f"✅ [监控] 监控结束")
        
    def extract_timeseries_data(self, max_patients=None):
        """主函数：启动多线程提取"""
        print(f"🚀 多线程时序数据提取")
        print(f"=" * 60)
        print(f"输入文件: {self.input_file}")
        print(f"输出文件: {self.output_file}")
        print(f"分块大小: {self.chunk_size:,} 行")
        print(f"队列大小: {self.queue_maxsize}")
        print(f"初始内存: {get_memory_usage():.1f} MB")
        
        file_size_mb = os.path.getsize(self.input_file) / 1024 / 1024
        print(f"文件大小: {file_size_mb:.1f} MB")
        print(f"=" * 60)
        
        total_start = time.time()
        
        try:
            # 启动三个线程
            with ThreadPoolExecutor(max_workers=3) as executor:
                # 提交线程任务
                producer_future = executor.submit(self.producer_thread)
                consumer_future = executor.submit(self.consumer_thread)
                monitor_future = executor.submit(self.monitor_thread)
                
                # 等待所有线程完成
                producer_future.result()
                consumer_future.result()
                monitor_future.result()
            
            total_read_time = time.time() - total_start
            
            print(f"\n✅ 多线程处理完成!")
            print(f"   总用时: {total_read_time:.1f}s")
            print(f"   总患者数: {len(self.patient_records):,}")
            print(f"   处理行数: {self.stats['rows_processed']:,}")
            print(f"   处理速度: {self.stats['rows_processed']/total_read_time:.0f} 行/s")
            
            # 分析和输出
            return self.analyze_and_output(max_patients)
            
        except Exception as e:
            print(f"❌ 多线程处理失败: {e}")
            return False
            
    def analyze_and_output(self, max_patients):
        """分析数据并输出"""
        print(f"\n🔍 分析时序数据分布...")
        analysis_start = time.time()
        
        # 统计记录数分布
        record_counts = Counter([len(records) for records in self.patient_records.values()])
        
        print(f"📊 患者记录分布:")
        total_records = 0
        for record_num in sorted(record_counts.keys()):
            patient_count = record_counts[record_num]
            records_sum = patient_count * record_num
            total_records += records_sum
            percentage = (patient_count / len(self.patient_records)) * 100
            print(f"   {record_num}条记录: {patient_count:,} 患者 ({records_sum:,} 记录, {percentage:.1f}%)")
        
        # 筛选时序患者
        multi_record_patients = {pid: records for pid, records in self.patient_records.items() 
                               if len(records) >= 2}
        three_record_patients = {pid: records for pid, records in self.patient_records.items() 
                               if len(records) >= 3}
        
        print(f"\n🎯 时序患者筛选:")
        print(f"   ≥2条记录: {len(multi_record_patients):,} 患者")
        print(f"   ≥3条记录: {len(three_record_patients):,} 患者")
        
        # 选择策略
        if len(three_record_patients) > 0:
            selected_patients = three_record_patients
            print(f"   ✅ 选择≥3条记录的患者: {len(selected_patients):,}")
        else:
            selected_patients = multi_record_patients
            print(f"   ✅ 选择≥2条记录的患者: {len(selected_patients):,}")
        
        # 采样
        if max_patients and len(selected_patients) > max_patients:
            import random
            random.seed(42)
            selected_ids = random.sample(list(selected_patients.keys()), max_patients)
            selected_patients = {pid: selected_patients[pid] for pid in selected_ids}
            print(f"   🎯 随机采样: {max_patients:,} 患者")
        
        analysis_time = time.time() - analysis_start
        print(f"   分析用时: {analysis_time:.1f}s")
        
        # 输出数据
        return self.output_data(selected_patients)
        
    def output_data(self, selected_patients):
        """输出数据到CSV"""
        print(f"\n💾 输出时序数据...")
        output_start = time.time()
        
        # 准备输出数据
        all_output_rows = []
        total_output_records = 0
        
        print(f"📊 准备 {len(selected_patients):,} 个患者的数据...")
        
        for patient_id, records in tqdm(selected_patients.items(), desc="整理患者数据"):
            # 按年份排序
            try:
                if len(records[0]) > 1:
                    records_sorted = sorted(records, key=lambda x: x[1] if x[1] is not None else 0)
                else:
                    records_sorted = records
            except:
                records_sorted = records
            
            all_output_rows.extend(records_sorted)
            total_output_records += len(records_sorted)
        
        print(f"✅ 数据整理完成: {total_output_records:,} 条记录")
        
        # 获取列名
        columns = [self.id_column] + [f'col_{i}' for i in range(1, len(all_output_rows[0]))]
        if hasattr(self, 'columns'):
            columns = self.columns
        
        # 保存数据
        try:
            print(f"📝 保存到文件...")
            final_df = pd.DataFrame(all_output_rows, columns=columns)
            final_df.to_csv(self.output_file, index=False, sep='\t', encoding='utf-8')
            
            output_time = time.time() - output_start
            file_size = os.path.getsize(self.output_file) / 1024 / 1024
            
            print(f"✅ 输出完成!")
            print(f"   输出文件: {self.output_file}")
            print(f"   文件大小: {file_size:.1f} MB")
            print(f"   输出用时: {output_time:.1f}s")
            print(f"   最终患者数: {len(selected_patients):,}")
            print(f"   最终记录数: {total_output_records:,}")
            
            return True
            
        except Exception as e:
            print(f"❌ 输出失败: {e}")
            return False

def main():
    """主函数"""
    input_file = "/root/work_speace/prediction/4-total_3years-uniqPerson_clean.csv"
    
    extractor = MultiThreadTimeSeriesExtractor(
        input_file=input_file,
        output_file="coronary_timeseries_data.csv",
        chunk_size=50000,   # 5万行一个chunk
        queue_maxsize=20    # 队列最大20个chunk
    )
    
    success = extractor.extract_timeseries_data(max_patients=None)
    
    if success:
        print("\n🎉 多线程提取成功!")
        print("📁 输出: coronary_timeseries_data.csv")
    else:
        print("\n❌ 提取失败")

if __name__ == "__main__":
    main()